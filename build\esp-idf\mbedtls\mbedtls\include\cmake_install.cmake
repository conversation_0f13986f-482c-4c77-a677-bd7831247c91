# Install script for directory: F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/wifi_TcpClient")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "TRUE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "F:/kfa/Espressif/tools/riscv32-esp-elf/esp-2021r2-patch3-8.4.0/riscv32-esp-elf/bin/riscv32-esp-elf-objdump.exe")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/mbedtls" TYPE FILE PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ FILES
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/aes.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/aesni.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/arc4.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/aria.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/asn1.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/asn1write.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/base64.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/bignum.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/blowfish.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/bn_mul.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/camellia.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ccm.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/certs.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/chacha20.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/chachapoly.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/check_config.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/cipher.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/cipher_internal.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/cmac.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/compat-1.3.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/config.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/config_psa.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/constant_time.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ctr_drbg.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/debug.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/des.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/dhm.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ecdh.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ecdsa.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ecjpake.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ecp.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ecp_internal.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/entropy.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/entropy_poll.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/error.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/gcm.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/havege.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/hkdf.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/hmac_drbg.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/md.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/md2.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/md4.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/md5.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/md_internal.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/memory_buffer_alloc.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/net.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/net_sockets.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/nist_kw.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/oid.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/padlock.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/pem.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/pk.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/pk_internal.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/pkcs11.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/pkcs12.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/pkcs5.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/platform.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/platform_time.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/platform_util.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/poly1305.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/psa_util.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ripemd160.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/rsa.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/rsa_internal.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/sha1.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/sha256.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/sha512.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ssl.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ssl_cache.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ssl_ciphersuites.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ssl_cookie.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ssl_internal.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/ssl_ticket.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/threading.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/timing.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/version.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/x509.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/x509_crl.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/x509_crt.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/x509_csr.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/mbedtls/xtea.h"
    )
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/psa" TYPE FILE PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ FILES
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_builtin_composites.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_builtin_primitives.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_compat.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_config.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_driver_common.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_driver_contexts_composites.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_driver_contexts_primitives.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_extra.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_platform.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_se_driver.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_sizes.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_struct.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_types.h"
    "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/mbedtls/include/psa/crypto_values.h"
    )
endif()

