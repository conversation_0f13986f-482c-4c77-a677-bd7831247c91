{"APP_BUILD_BOOTLOADER": true, "APP_BUILD_GENERATE_BINARIES": true, "APP_BUILD_TYPE_APP_2NDBOOT": true, "APP_BUILD_TYPE_ELF_RAM": false, "APP_BUILD_USE_FLASH_SECTIONS": true, "BOOTLOADER_APP_ROLLBACK_ENABLE": false, "BOOTLOADER_APP_TEST": false, "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG": false, "BOOTLOADER_COMPILER_OPTIMIZATION_NONE": false, "BOOTLOADER_COMPILER_OPTIMIZATION_PERF": false, "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE": true, "BOOTLOADER_CUSTOM_RESERVE_RTC": false, "BOOTLOADER_FACTORY_RESET": false, "BOOTLOADER_FLASH_XMC_SUPPORT": true, "BOOTLOADER_LOG_LEVEL": 3, "BOOTLOADER_LOG_LEVEL_DEBUG": false, "BOOTLOADER_LOG_LEVEL_ERROR": false, "BOOTLOADER_LOG_LEVEL_INFO": true, "BOOTLOADER_LOG_LEVEL_NONE": false, "BOOTLOADER_LOG_LEVEL_VERBOSE": false, "BOOTLOADER_LOG_LEVEL_WARN": false, "BOOTLOADER_OFFSET_IN_FLASH": 0, "BOOTLOADER_RESERVE_RTC_SIZE": 0, "BOOTLOADER_SKIP_VALIDATE_ALWAYS": false, "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP": false, "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON": false, "BOOTLOADER_VDDSDIO_BOOST_1_9V": true, "BOOTLOADER_WDT_DISABLE_IN_USER_CODE": false, "BOOTLOADER_WDT_ENABLE": true, "BOOTLOADER_WDT_TIME_MS": 9000, "BOOT_ROM_LOG_ALWAYS_OFF": false, "BOOT_ROM_LOG_ALWAYS_ON": true, "BOOT_ROM_LOG_ON_GPIO_HIGH": false, "BOOT_ROM_LOG_ON_GPIO_LOW": false, "COMPILER_CXX_EXCEPTIONS": false, "COMPILER_CXX_RTTI": false, "COMPILER_DISABLE_GCC8_WARNINGS": false, "COMPILER_DUMP_RTL_FILES": false, "COMPILER_HIDE_PATHS_MACROS": true, "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE": false, "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE": true, "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT": false, "COMPILER_OPTIMIZATION_ASSERTION_LEVEL": 2, "COMPILER_OPTIMIZATION_CHECKS_SILENT": false, "COMPILER_OPTIMIZATION_DEFAULT": true, "COMPILER_OPTIMIZATION_NONE": false, "COMPILER_OPTIMIZATION_PERF": false, "COMPILER_OPTIMIZATION_SIZE": false, "COMPILER_SAVE_RESTORE_LIBCALLS": false, "COMPILER_STACK_CHECK_MODE_ALL": false, "COMPILER_STACK_CHECK_MODE_NONE": true, "COMPILER_STACK_CHECK_MODE_NORM": false, "COMPILER_STACK_CHECK_MODE_STRONG": false, "COMPILER_WARN_WRITE_STRINGS": false, "EFUSE_CUSTOM_TABLE": false, "EFUSE_MAX_BLK_LEN": 256, "EFUSE_VIRTUAL": false, "ESP32C3_BROWNOUT_DET": true, "ESP32C3_BROWNOUT_DET_LVL": 7, "ESP32C3_BROWNOUT_DET_LVL_SEL_2": false, "ESP32C3_BROWNOUT_DET_LVL_SEL_3": false, "ESP32C3_BROWNOUT_DET_LVL_SEL_4": false, "ESP32C3_BROWNOUT_DET_LVL_SEL_5": false, "ESP32C3_BROWNOUT_DET_LVL_SEL_6": false, "ESP32C3_BROWNOUT_DET_LVL_SEL_7": true, "ESP32C3_DEBUG_OCDAWARE": true, "ESP32C3_DEFAULT_CPU_FREQ_160": true, "ESP32C3_DEFAULT_CPU_FREQ_80": false, "ESP32C3_DEFAULT_CPU_FREQ_MHZ": 160, "ESP32C3_NO_BLOBS": false, "ESP32C3_REV_MIN": 3, "ESP32C3_REV_MIN_0": false, "ESP32C3_REV_MIN_1": false, "ESP32C3_REV_MIN_2": false, "ESP32C3_REV_MIN_3": true, "ESP32C3_RTC_CLK_CAL_CYCLES": 1024, "ESP32C3_RTC_CLK_SRC_EXT_CRYS": false, "ESP32C3_RTC_CLK_SRC_EXT_OSC": false, "ESP32C3_RTC_CLK_SRC_INT_8MD256": false, "ESP32C3_RTC_CLK_SRC_INT_RC": true, "ESP32C3_TIME_SYSCALL_USE_NONE": false, "ESP32C3_TIME_SYSCALL_USE_RTC": false, "ESP32C3_TIME_SYSCALL_USE_RTC_SYSTIMER": true, "ESP32C3_TIME_SYSCALL_USE_SYSTIMER": false, "ESP32C3_UNIVERSAL_MAC_ADDRESSES": 4, "ESP32C3_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP32C3_UNIVERSAL_MAC_ADDRESSES_TWO": false, "ESPTOOLPY_AFTER": "hard_reset", "ESPTOOLPY_AFTER_NORESET": false, "ESPTOOLPY_AFTER_RESET": true, "ESPTOOLPY_BAUD_OTHER_VAL": 115200, "ESPTOOLPY_BEFORE": "default_reset", "ESPTOOLPY_BEFORE_NORESET": false, "ESPTOOLPY_BEFORE_RESET": true, "ESPTOOLPY_FLASHFREQ": "80m", "ESPTOOLPY_FLASHFREQ_20M": false, "ESPTOOLPY_FLASHFREQ_26M": false, "ESPTOOLPY_FLASHFREQ_40M": false, "ESPTOOLPY_FLASHFREQ_80M": true, "ESPTOOLPY_FLASHMODE": "dio", "ESPTOOLPY_FLASHMODE_DIO": true, "ESPTOOLPY_FLASHMODE_DOUT": false, "ESPTOOLPY_FLASHMODE_QIO": false, "ESPTOOLPY_FLASHMODE_QOUT": false, "ESPTOOLPY_FLASHSIZE": "2MB", "ESPTOOLPY_FLASHSIZE_16MB": false, "ESPTOOLPY_FLASHSIZE_1MB": false, "ESPTOOLPY_FLASHSIZE_2MB": true, "ESPTOOLPY_FLASHSIZE_4MB": false, "ESPTOOLPY_FLASHSIZE_8MB": false, "ESPTOOLPY_FLASHSIZE_DETECT": true, "ESPTOOLPY_FLASH_SAMPLE_MODE_STR": true, "ESPTOOLPY_MONITOR_BAUD": 115200, "ESPTOOLPY_MONITOR_BAUD_115200B": true, "ESPTOOLPY_MONITOR_BAUD_230400B": false, "ESPTOOLPY_MONITOR_BAUD_2MB": false, "ESPTOOLPY_MONITOR_BAUD_57600B": false, "ESPTOOLPY_MONITOR_BAUD_921600B": false, "ESPTOOLPY_MONITOR_BAUD_9600B": false, "ESPTOOLPY_MONITOR_BAUD_CONSOLE": false, "ESPTOOLPY_MONITOR_BAUD_OTHER": false, "ESPTOOLPY_MONITOR_BAUD_OTHER_VAL": 115200, "ESPTOOLPY_NO_STUB": false, "ESP_CONSOLE_NONE": false, "ESP_CONSOLE_SECONDARY_NONE": false, "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG": true, "ESP_CONSOLE_UART": true, "ESP_CONSOLE_UART_BAUDRATE": 115200, "ESP_CONSOLE_UART_CUSTOM": false, "ESP_CONSOLE_UART_DEFAULT": true, "ESP_CONSOLE_UART_NUM": 0, "ESP_CONSOLE_USB_SERIAL_JTAG": false, "ESP_DEBUG_STUBS_ENABLE": false, "ESP_ERR_TO_NAME_LOOKUP": true, "ESP_INT_WDT": true, "ESP_INT_WDT_TIMEOUT_MS": 300, "ESP_MAC_ADDR_UNIVERSE_BT": true, "ESP_MAC_ADDR_UNIVERSE_ETH": true, "ESP_MAC_ADDR_UNIVERSE_WIFI_AP": true, "ESP_MAC_ADDR_UNIVERSE_WIFI_STA": true, "ESP_MAIN_TASK_AFFINITY": 0, "ESP_MAIN_TASK_AFFINITY_CPU0": true, "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY": false, "ESP_MAIN_TASK_STACK_SIZE": 3584, "ESP_MINIMAL_SHARED_STACK_SIZE": 2048, "ESP_PANIC_HANDLER_IRAM": false, "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND": false, "ESP_SLEEP_GPIO_RESET_WORKAROUND": true, "ESP_SLEEP_POWER_DOWN_FLASH": true, "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP": true, "ESP_SYSTEM_CHECK_INT_LEVEL_4": true, "ESP_SYSTEM_EVENT_QUEUE_SIZE": 32, "ESP_SYSTEM_EVENT_TASK_STACK_SIZE": 2304, "ESP_SYSTEM_GDBSTUB_RUNTIME": false, "ESP_SYSTEM_MEMPROT_CPU_PREFETCH_PAD_SIZE": 16, "ESP_SYSTEM_MEMPROT_DEPCHECK": true, "ESP_SYSTEM_MEMPROT_FEATURE": true, "ESP_SYSTEM_MEMPROT_FEATURE_LOCK": true, "ESP_SYSTEM_MEMPROT_MEM_ALIGN_SIZE": 512, "ESP_SYSTEM_PANIC_GDBSTUB": false, "ESP_SYSTEM_PANIC_PRINT_HALT": false, "ESP_SYSTEM_PANIC_PRINT_REBOOT": true, "ESP_SYSTEM_PANIC_SILENT_REBOOT": false, "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK": true, "ESP_SYSTEM_SINGLE_CORE_MODE": true, "ESP_SYSTEM_USE_EH_FRAME": false, "ESP_TASK_WDT": true, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0": true, "ESP_TASK_WDT_PANIC": false, "ESP_TASK_WDT_TIMEOUT_S": 5, "FREERTOS_ASSERT_DISABLE": false, "FREERTOS_ASSERT_FAIL_ABORT": true, "FREERTOS_ASSERT_FAIL_PRINT_CONTINUE": false, "FREERTOS_ASSERT_ON_UNTESTED_FUNCTION": true, "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER": true, "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE": false, "FREERTOS_CHECK_STACKOVERFLOW_CANARY": true, "FREERTOS_CHECK_STACKOVERFLOW_NONE": false, "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL": false, "FREERTOS_CORETIMER_SYSTIMER_LVL1": true, "FREERTOS_CORETIMER_SYSTIMER_LVL3": false, "FREERTOS_DEBUG_OCDAWARE": true, "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP": false, "FREERTOS_ENABLE_TASK_SNAPSHOT": true, "FREERTOS_GENERATE_RUN_TIME_STATS": false, "FREERTOS_HZ": 100, "FREERTOS_IDLE_TASK_STACKSIZE": 1536, "FREERTOS_INTERRUPT_BACKTRACE": true, "FREERTOS_ISR_STACKSIZE": 1536, "FREERTOS_LEGACY_HOOKS": false, "FREERTOS_MAX_TASK_NAME_LEN": 16, "FREERTOS_NO_AFFINITY": 2147483647, "FREERTOS_OPTIMIZED_SCHEDULER": true, "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH": false, "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH": false, "FREERTOS_QUEUE_REGISTRY_SIZE": 0, "FREERTOS_SUPPORT_STATIC_ALLOCATION": true, "FREERTOS_SYSTICK_USES_SYSTIMER": true, "FREERTOS_TASK_FUNCTION_WRAPPER": true, "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS": 1, "FREERTOS_TICK_SUPPORT_SYSTIMER": true, "FREERTOS_TIMER_QUEUE_LENGTH": 10, "FREERTOS_TIMER_TASK_PRIORITY": 1, "FREERTOS_TIMER_TASK_STACK_DEPTH": 2048, "FREERTOS_UNICORE": true, "FREERTOS_USE_TRACE_FACILITY": false, "FREERTOS_WATCHPOINT_END_OF_STACK": false, "HAL_ASSERTION_DISABLE": false, "HAL_ASSERTION_ENABLE": false, "HAL_ASSERTION_EQUALS_SYSTEM": true, "HAL_ASSERTION_SILIENT": false, "HAL_DEFAULT_ASSERTION_LEVEL": 2, "IDF_CMAKE": true, "IDF_FIRMWARE_CHIP_ID": 5, "IDF_TARGET": "esp32c3", "IDF_TARGET_ARCH_RISCV": true, "IDF_TARGET_ESP32C3": true, "LEGACY_INCLUDE_COMMON_HEADERS": false, "LOG_COLORS": true, "LOG_DEFAULT_LEVEL": 3, "LOG_DEFAULT_LEVEL_DEBUG": false, "LOG_DEFAULT_LEVEL_ERROR": false, "LOG_DEFAULT_LEVEL_INFO": true, "LOG_DEFAULT_LEVEL_NONE": false, "LOG_DEFAULT_LEVEL_VERBOSE": false, "LOG_DEFAULT_LEVEL_WARN": false, "LOG_MAXIMUM_EQUALS_DEFAULT": true, "LOG_MAXIMUM_LEVEL": 3, "LOG_MAXIMUM_LEVEL_DEBUG": false, "LOG_MAXIMUM_LEVEL_VERBOSE": false, "LOG_TIMESTAMP_SOURCE_RTOS": true, "LOG_TIMESTAMP_SOURCE_SYSTEM": false, "NEWLIB_NANO_FORMAT": false, "NEWLIB_STDIN_LINE_ENDING_CR": true, "NEWLIB_STDIN_LINE_ENDING_CRLF": false, "NEWLIB_STDIN_LINE_ENDING_LF": false, "NEWLIB_STDOUT_LINE_ENDING_CR": false, "NEWLIB_STDOUT_LINE_ENDING_CRLF": true, "NEWLIB_STDOUT_LINE_ENDING_LF": false, "PARTITION_TABLE_CUSTOM": false, "PARTITION_TABLE_CUSTOM_FILENAME": "partitions.csv", "PARTITION_TABLE_FILENAME": "partitions_singleapp.csv", "PARTITION_TABLE_MD5": true, "PARTITION_TABLE_OFFSET": 32768, "PARTITION_TABLE_SINGLE_APP": true, "PARTITION_TABLE_SINGLE_APP_LARGE": false, "PARTITION_TABLE_TWO_OTA": false, "RTC_CLOCK_BBPLL_POWER_ON_WITH_USB": true, "SDK_TOOLCHAIN_SUPPORTS_TIME_WIDE_64_BITS": false, "SDK_TOOLPREFIX": "riscv32-esp-elf-", "SECURE_BOOT": false, "SECURE_BOOT_SUPPORTS_RSA": true, "SECURE_FLASH_ENC_ENABLED": false, "SECURE_SIGNED_APPS_NO_SECURE_BOOT": false, "SECURE_TARGET_HAS_SECURE_ROM_DL_MODE": true, "SPI_FLASH_AUTO_SUSPEND": false, "SPI_FLASH_BYPASS_BLOCK_ERASE": false, "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED": false, "SPI_FLASH_DANGEROUS_WRITE_ABORTS": true, "SPI_FLASH_DANGEROUS_WRITE_ALLOWED": false, "SPI_FLASH_DANGEROUS_WRITE_FAILS": false, "SPI_FLASH_ENABLE_COUNTERS": false, "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE": true, "SPI_FLASH_ERASE_YIELD_DURATION_MS": 20, "SPI_FLASH_ERASE_YIELD_TICKS": 1, "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST": false, "SPI_FLASH_ROM_DRIVER_PATCH": true, "SPI_FLASH_ROM_IMPL": false, "SPI_FLASH_SHARE_SPI1_BUS": false, "SPI_FLASH_SIZE_OVERRIDE": false, "SPI_FLASH_SUPPORT_BOYA_CHIP": true, "SPI_FLASH_SUPPORT_GD_CHIP": true, "SPI_FLASH_SUPPORT_ISSI_CHIP": true, "SPI_FLASH_SUPPORT_MXIC_CHIP": true, "SPI_FLASH_SUPPORT_TH_CHIP": true, "SPI_FLASH_SUPPORT_WINBOND_CHIP": true, "SPI_FLASH_USE_LEGACY_IMPL": false, "SPI_FLASH_VERIFY_WRITE": false, "SPI_FLASH_WRITE_CHUNK_SIZE": 8192, "SPI_FLASH_YIELD_DURING_ERASE": true}