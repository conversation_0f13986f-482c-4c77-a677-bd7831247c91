{"sources": [{"file": "D:/Personal/Downloads/station-code-master/build/CMakeFiles/bootloader"}, {"file": "D:/Personal/Downloads/station-code-master/build/CMakeFiles/bootloader.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/Personal/Downloads/station-code-master/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}