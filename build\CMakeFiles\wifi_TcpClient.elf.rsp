CMakeFiles/wifi_TcpClient.elf.dir/project_elf_src_esp32c3.c.obj  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/efuse/libefuse.a  esp-idf/esp_ipc/libesp_ipc.a  esp-idf/driver/libdriver.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/app_update/libapp_update.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/pthread/libpthread.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/vfs/libvfs.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/tcpip_adapter/libtcpip_adapter.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/esp_event/libesp_event.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/console/libconsole.a  esp-idf/lwip/liblwip.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/riscv/libriscv.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/cxx/libcxx.a  esp-idf/app_trace/libapp_trace.a  esp-idf/asio/libasio.a  esp-idf/cbor/libcbor.a  esp-idf/unity/libunity.a  esp-idf/cmock/libcmock.a  esp-idf/coap/libcoap.a  esp-idf/nghttp/libnghttp.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/esp_adc_cal/libesp_adc_cal.a  esp-idf/esp_hid/libesp_hid.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/esp_lcd/libesp_lcd.a  esp-idf/protobuf-c/libprotobuf-c.a  esp-idf/protocomm/libprotocomm.a  esp-idf/mdns/libmdns.a  esp-idf/esp_local_ctrl/libesp_local_ctrl.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_serial_slave_link/libesp_serial_slave_link.a  esp-idf/esp_websocket_client/libesp_websocket_client.a  esp-idf/expat/libexpat.a  esp-idf/wear_levelling/libwear_levelling.a  esp-idf/fatfs/libfatfs.a  esp-idf/freemodbus/libfreemodbus.a  esp-idf/jsmn/libjsmn.a  esp-idf/json/libjson.a  esp-idf/libsodium/liblibsodium.a  esp-idf/mqtt/libmqtt.a  esp-idf/openssl/libopenssl.a  esp-idf/spiffs/libspiffs.a  esp-idf/wifi_provisioning/libwifi_provisioning.a  esp-idf/led/libled.a  esp-idf/wifi/libwifi.a  esp-idf/tcp/libtcp.a  esp-idf/lcd/liblcd.a  -Wl,--cref  -Wl,--Map="D:/Personal/Downloads/station-code-master/build/wifi_TcpClient.map"  -Wl,--gc-sections  -fno-rtti  -fno-lto  esp-idf/unity/libunity.a  esp-idf/wear_levelling/libwear_levelling.a  esp-idf/protocomm/libprotocomm.a  esp-idf/protobuf-c/libprotobuf-c.a  esp-idf/mdns/libmdns.a  esp-idf/json/libjson.a  esp-idf/wifi/libwifi.a  esp-idf/tcp/libtcp.a  esp-idf/lcd/liblcd.a  esp-idf/led/libled.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/efuse/libefuse.a  esp-idf/esp_ipc/libesp_ipc.a  esp-idf/driver/libdriver.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/app_update/libapp_update.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/pthread/libpthread.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/vfs/libvfs.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/tcpip_adapter/libtcpip_adapter.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/esp_event/libesp_event.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/console/libconsole.a  esp-idf/lwip/liblwip.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/riscv/libriscv.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/cxx/libcxx.a  esp-idf/app_trace/libapp_trace.a  esp-idf/nghttp/libnghttp.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_serial_slave_link/libesp_serial_slave_link.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcoexist.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcore.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libespnow.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libmesh.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libnet80211.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libpp.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libsmartconfig.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libwapi.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/efuse/libefuse.a  esp-idf/esp_ipc/libesp_ipc.a  esp-idf/driver/libdriver.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/app_update/libapp_update.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/pthread/libpthread.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/vfs/libvfs.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/tcpip_adapter/libtcpip_adapter.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/esp_event/libesp_event.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/console/libconsole.a  esp-idf/lwip/liblwip.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/riscv/libriscv.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/cxx/libcxx.a  esp-idf/app_trace/libapp_trace.a  esp-idf/nghttp/libnghttp.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_serial_slave_link/libesp_serial_slave_link.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcoexist.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcore.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libespnow.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libmesh.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libnet80211.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libpp.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libsmartconfig.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libwapi.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/efuse/libefuse.a  esp-idf/esp_ipc/libesp_ipc.a  esp-idf/driver/libdriver.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/app_update/libapp_update.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/pthread/libpthread.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/vfs/libvfs.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/tcpip_adapter/libtcpip_adapter.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/esp_event/libesp_event.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/console/libconsole.a  esp-idf/lwip/liblwip.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/riscv/libriscv.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/cxx/libcxx.a  esp-idf/app_trace/libapp_trace.a  esp-idf/nghttp/libnghttp.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_serial_slave_link/libesp_serial_slave_link.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcoexist.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcore.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libespnow.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libmesh.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libnet80211.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libpp.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libsmartconfig.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libwapi.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/efuse/libefuse.a  esp-idf/esp_ipc/libesp_ipc.a  esp-idf/driver/libdriver.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/app_update/libapp_update.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/pthread/libpthread.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/vfs/libvfs.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/tcpip_adapter/libtcpip_adapter.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/esp_event/libesp_event.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/console/libconsole.a  esp-idf/lwip/liblwip.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/riscv/libriscv.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/cxx/libcxx.a  esp-idf/app_trace/libapp_trace.a  esp-idf/nghttp/libnghttp.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_serial_slave_link/libesp_serial_slave_link.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcoexist.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcore.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libespnow.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libmesh.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libnet80211.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libpp.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libsmartconfig.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libwapi.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/efuse/libefuse.a  esp-idf/esp_ipc/libesp_ipc.a  esp-idf/driver/libdriver.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/app_update/libapp_update.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/pthread/libpthread.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/vfs/libvfs.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/tcpip_adapter/libtcpip_adapter.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/esp_event/libesp_event.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/console/libconsole.a  esp-idf/lwip/liblwip.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/riscv/libriscv.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/cxx/libcxx.a  esp-idf/app_trace/libapp_trace.a  esp-idf/nghttp/libnghttp.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_serial_slave_link/libesp_serial_slave_link.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcoexist.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcore.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libespnow.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libmesh.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libnet80211.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libpp.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libsmartconfig.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libwapi.a  esp-idf/esp_ringbuf/libesp_ringbuf.a  esp-idf/efuse/libefuse.a  esp-idf/esp_ipc/libesp_ipc.a  esp-idf/driver/libdriver.a  esp-idf/esp_pm/libesp_pm.a  esp-idf/mbedtls/libmbedtls.a  esp-idf/app_update/libapp_update.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/nvs_flash/libnvs_flash.a  esp-idf/pthread/libpthread.a  esp-idf/esp_gdbstub/libesp_gdbstub.a  esp-idf/espcoredump/libespcoredump.a  esp-idf/esp_phy/libesp_phy.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/hal/libhal.a  esp-idf/vfs/libvfs.a  esp-idf/esp_eth/libesp_eth.a  esp-idf/tcpip_adapter/libtcpip_adapter.a  esp-idf/esp_netif/libesp_netif.a  esp-idf/esp_event/libesp_event.a  esp-idf/wpa_supplicant/libwpa_supplicant.a  esp-idf/esp_wifi/libesp_wifi.a  esp-idf/console/libconsole.a  esp-idf/lwip/liblwip.a  esp-idf/log/liblog.a  esp-idf/heap/libheap.a  esp-idf/soc/libsoc.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/riscv/libriscv.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_timer/libesp_timer.a  esp-idf/freertos/libfreertos.a  esp-idf/newlib/libnewlib.a  esp-idf/cxx/libcxx.a  esp-idf/app_trace/libapp_trace.a  esp-idf/nghttp/libnghttp.a  esp-idf/esp-tls/libesp-tls.a  esp-idf/tcp_transport/libtcp_transport.a  esp-idf/esp_http_client/libesp_http_client.a  esp-idf/esp_http_server/libesp_http_server.a  esp-idf/esp_https_ota/libesp_https_ota.a  esp-idf/sdmmc/libsdmmc.a  esp-idf/esp_serial_slave_link/libesp_serial_slave_link.a  esp-idf/mbedtls/mbedtls/library/libmbedtls.a  esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a  esp-idf/mbedtls/mbedtls/library/libmbedx509.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcoexist.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libcore.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libespnow.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libmesh.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libnet80211.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libpp.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libsmartconfig.a  F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3/libwapi.a  -u esp_app_desc  -u pthread_include_pthread_impl  -u pthread_include_pthread_cond_impl  -u pthread_include_pthread_local_storage_impl  -u pthread_include_pthread_rwlock_impl  -L "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_phy/lib/esp32c3"  -u include_esp_phy_override  -lphy  -lbtbb  esp-idf/esp_phy/libesp_phy.a  -lphy  -lbtbb  esp-idf/esp_phy/libesp_phy.a  -lphy  -lbtbb  -u start_app  -L "D:/Personal/Downloads/station-code-master/build/esp-idf/esp_system/ld"  -T memory.ld  -T sections.ld  -u __ubsan_include  -L "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3/ld"  -T esp32c3.rom.ld  -T esp32c3.rom.api.ld  -T esp32c3.rom.libgcc.ld  -T esp32c3.rom.newlib.ld  -T esp32c3.rom.version.ld  -T esp32c3.rom.newlib-time.ld  -T esp32c3.rom.eco3.ld  -u __assert_func  -u vfs_include_syscalls_impl  -L "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/lib/esp32c3"  -L "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/ld"  -T esp32c3.peripherals.ld  -Wl,--undefined=uxTopUsedPriority  -u app_main  -lm  esp-idf/newlib/libnewlib.a  -u newlib_include_heap_impl  -u newlib_include_syscalls_impl  -u newlib_include_pthread_impl  -u newlib_include_assert_impl  -Wl,--wrap=_Unwind_SetEnableExceptionFdeSorting  -Wl,--wrap=__register_frame_info_bases  -Wl,--wrap=__register_frame_info  -Wl,--wrap=__register_frame  -Wl,--wrap=__register_frame_info_table_bases  -Wl,--wrap=__register_frame_info_table  -Wl,--wrap=__register_frame_table  -Wl,--wrap=__deregister_frame_info_bases  -Wl,--wrap=__deregister_frame_info  -Wl,--wrap=_Unwind_Find_FDE  -Wl,--wrap=_Unwind_GetGR  -Wl,--wrap=_Unwind_GetCFA  -Wl,--wrap=_Unwind_GetIP  -Wl,--wrap=_Unwind_GetIPInfo  -Wl,--wrap=_Unwind_GetRegionStart  -Wl,--wrap=_Unwind_GetDataRelBase  -Wl,--wrap=_Unwind_GetTextRelBase  -Wl,--wrap=_Unwind_SetIP  -Wl,--wrap=_Unwind_SetGR  -Wl,--wrap=_Unwind_GetLanguageSpecificData  -Wl,--wrap=_Unwind_FindEnclosingFunction  -Wl,--wrap=_Unwind_Resume  -Wl,--wrap=_Unwind_RaiseException  -Wl,--wrap=_Unwind_DeleteException  -Wl,--wrap=_Unwind_ForcedUnwind  -Wl,--wrap=_Unwind_Resume_or_Rethrow  -Wl,--wrap=_Unwind_Backtrace  -Wl,--wrap=__cxa_call_unexpected  -Wl,--wrap=__gxx_personality_v0  -u __cxa_guard_dummy  -lstdc++  esp-idf/pthread/libpthread.a  -lgcc  esp-idf/cxx/libcxx.a  -u __cxx_fatal_exception  esp-idf/app_trace/libapp_trace.a  -lgcov  esp-idf/app_trace/libapp_trace.a  -lgcov  -lc