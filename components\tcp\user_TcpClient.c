#include "led.h"
#include "user_TcpClient.h"

// 仅声明全局在线状态变量，避免重复定义
extern unsigned char g_online_state;
extern unsigned char g_whiteled_state;

EventGroupHandle_t g_tcp_event_group;   // WiFi连接成功信号量

// 重连相关的全局变量和统计信息
static uint32_t g_reconnect_count = 0;          // 重连计数器
static uint32_t g_connection_failed_count = 0;  // 连接失败计数器
static TaskHandle_t g_current_recv_task = NULL; // 当前接收任务句柄
static bool g_is_connecting = false;            // 是否正在连接中标志
static TickType_t g_last_reconnect_time = 0;    // 上次重连时间
static const TickType_t RECONNECT_MIN_INTERVAL = pdMS_TO_TICKS(5000); // 最小重连间隔5秒

// TCP客户端连接线程
void _tcp_client_connect(void *pvParameters)
{
    g_tcp_event_group = xEventGroupCreate();
    // 默认离线状态
    g_online_state = enNO;
    
    g_rxtx_need_restart = false;
    g_is_connecting = false;
    g_current_recv_task = NULL;
    
    //ESP_LOGI(TAG, "[重连系统] TCP客户端连接任务启动");
    
    // 第一步：等待WiFi连接
    //ESP_LOGI(TAG, "[重连系统] 等待WiFi连接...");
    xEventGroupWaitBits(g_tcp_event_group, WIFI_CONNECTED_BIT, false, true, portMAX_DELAY);
    //ESP_LOGI(TAG, "[重连系统] WiFi连接成功，开始TCP连接流程");

    // 延时3S准备创建client
    vTaskDelay(3000 / portTICK_RATE_MS);
    
    // 主循环 - 连接管理
    while (1)
    {   
        // 检查是否需要重连 - 只有在明确离线或需要重启时才重连
        if ((g_rxtx_need_restart || g_online_state == enNO) && !g_is_connecting)
        {
            // 检查重连间隔
            TickType_t current_time = xTaskGetTickCount();
            if (g_last_reconnect_time != 0 && 
                (current_time - g_last_reconnect_time) < RECONNECT_MIN_INTERVAL)
            {
                //ESP_LOGW(TAG, "[重连系统] 重连间隔过短，等待中... 剩余时间: %d ms", 
                //         (int)((RECONNECT_MIN_INTERVAL - (current_time - g_last_reconnect_time)) * portTICK_PERIOD_MS));
                vTaskDelay(1000 / portTICK_RATE_MS);
                continue;
            }
            
            g_last_reconnect_time = current_time;
            g_reconnect_count++;
            
            //ESP_LOGI(TAG, "[重连系统] 开始重连流程 - 重连次数: %d, 连接失败次数: %d", 
            //         g_reconnect_count, g_connection_failed_count);
            
            // 清理之前的接收任务
            if (g_current_recv_task != NULL)
            {
                //ESP_LOGI(TAG, "[重连系统] 终止之前的接收任务");
                vTaskDelete(g_current_recv_task);
                g_current_recv_task = NULL;
                vTaskDelay(200 / portTICK_RATE_MS); // 等待任务完全删除
            }
            
            // 关闭之前的socket连接
            _close_socket();
            
            // 重置标志
            g_rxtx_need_restart = false;
            g_is_connecting = true;
            g_online_state = enNO; // 明确设置为离线状态
            
            //ESP_LOGI(TAG, "[重连系统] 尝试创建TCP连接...");
            
            // 创建TCP客户端连接
            esp_err_t socket_ret = _create_tcp_client();
            
            if (socket_ret == ESP_FAIL)
            {
                g_connection_failed_count++;
                g_online_state = enNO;
                g_is_connecting = false;
                
                //ESP_LOGE(TAG, "[重连系统] TCP连接创建失败 - 总重连次数: %d, 失败次数: %d", 
                //         g_reconnect_count, g_connection_failed_count);
                
                // 连接失败，等待后重试
                vTaskDelay(3000 / portTICK_RATE_MS);
                continue;
            }
            else
            {
                //ESP_LOGI(TAG, "[重连系统] TCP连接创建成功！");
                
                // 创建新的数据接收任务
                BaseType_t task_ret = xTaskCreate(&_client_recv_data, "client_recv_data", 
                                                  4096, NULL, 4, &g_current_recv_task);
                
                if (task_ret != pdPASS)
                {
                    //ESP_LOGE(TAG, "[重连系统] 接收任务创建失败！");
                    g_online_state = enNO;
                    g_is_connecting = false;
                    _close_socket();
                    vTaskDelay(3000 / portTICK_RATE_MS);
                    continue;
                }
                else
                {
                    //ESP_LOGI(TAG, "[重连系统] 接收任务创建成功，连接完全建立");
                    //ESP_LOGI(TAG, "[重连系统] 连接统计 - 成功连接次数: %d, 失败次数: %d", 
                    //         g_reconnect_count - g_connection_failed_count, g_connection_failed_count);
                    
                    // 连接成功，更新状态
                    g_online_state = enYES;
                    g_is_connecting = false;
                }
            }
        }
        
        // 监控连接状态 - 只有在真正在线且不在连接过程中时才输出
        if (g_online_state == enYES && !g_is_connecting && g_current_recv_task != NULL)
        {
            static uint32_t status_log_counter = 0;
            status_log_counter++;
            
            // 每30秒检查一次连接状态
            if (status_log_counter % 10 == 0) // 3秒循环 * 10 = 30秒
            {
                bool connection_valid = _check_socket_connection(g_connect_socket);
                if (!connection_valid)
                {
                    //ESP_LOGW(TAG, "[连接监控] 检测到连接断开，主动触发重连");
                    g_online_state = enNO;
                    g_rxtx_need_restart = true;
                }
                else
                {
                    //ESP_LOGI(TAG, "[连接监控] 连接正常 - Socket: %d, 在线时长: %d分钟, 总重连次数: %d", 
                    //         g_connect_socket, status_log_counter * 3 / 60, g_reconnect_count);
                }
            }
        }
        
        // 循环延时
        vTaskDelay(3000 / portTICK_RATE_MS);
    }

    vTaskDelete(NULL);
}

// 创建TCP client
esp_err_t _create_tcp_client()
{
    //ESP_LOGI(TAG, "[TCP连接] 准备连接服务器 IP: %s, 端口: %d", g_sserver_ip, g_server_port);
    
    // 第一步：创建socket
    g_connect_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (g_connect_socket < 0)
    {
        // 打印错误信息
        _show_socket_error_reason("create client socket", g_connect_socket);
        //ESP_LOGE(TAG, "[TCP连接] Socket创建失败");
        return ESP_FAIL;
    }
    
    //ESP_LOGI(TAG, "[TCP连接] Socket创建成功，socket fd: %d", g_connect_socket);
    
    // 配置连接服务器信息
    g_server_addr.sin_family = AF_INET;
    g_server_addr.sin_port = lwip_htons(g_server_port);
    g_server_addr.sin_addr.s_addr = inet_addr((char*)(&g_sserver_ip));
    
    //ESP_LOGI(TAG, "[TCP连接] 正在连接服务器...");
    
    // 第二步：连接服务器
    if (connect(g_connect_socket, (struct sockaddr *)&g_server_addr, sizeof(g_server_addr)) < 0)
    {
        // 打印错误信息
        _show_socket_error_reason("client connect", g_connect_socket);
        //ESP_LOGE(TAG, "[TCP连接] 服务器连接失败！");
        
        // 连接失败后，关闭之前创建的socket
        g_online_state = enNO;
        close(g_connect_socket);
        g_connect_socket = -1;
        return ESP_FAIL;
    }
    
    //ESP_LOGI(TAG, "[TCP连接] 服务器连接成功！");
    g_online_state = enYES;
    return ESP_OK;
}

// TCP数据接收任务
void _client_recv_data(void *pvParameters)
{
    bool bret = true; // 处理数据标记
    unsigned int len = 0;            // 长度
    unsigned char recdata[1500];    // 缓存        
    uint32_t recv_count = 0;        // 接收数据包计数
    uint32_t recv_bytes_total = 0;  // 接收字节总数
    int consecutive_errors = 0;     // 连续错误计数
    
    //ESP_LOGI(TAG, "[数据接收] 接收任务开始运行，Socket: %d", g_connect_socket);
    
    // 清空缓存
    memset(recdata, 0x00, sizeof(recdata));
    
    while (1)
    {
        // 获取服务器数据
        len = recv(g_connect_socket, recdata, 1500, 0);
        
        if (len > 0)
        {
            recv_count++;
            recv_bytes_total += len;
            consecutive_errors = 0; // 重置错误计数
            
            // 每收到100个数据包或每隔一段时间输出统计日志
            if (recv_count % 50 == 0 || recv_count <= 5)
            {
                //ESP_LOGI(TAG, "[数据接收] 收到数据包 #%d, 长度: %d字节, 累计: %d字节", 
                //         recv_count, len, recv_bytes_total);
            }
            
            // 处理接收到的数据
            bret = _receive_netdata(recdata, 0, len);
            
            if (!bret)
            {
                //ESP_LOGW(TAG, "[数据接收] 数据处理失败");
            }
            
            // 清空缓存为下次接收做准备
            memset(recdata, 0x00, sizeof(recdata));
        }
        else if (len == 0)
        {
            // 连接被对方关闭
            //ESP_LOGW(TAG, "[数据接收] 服务器主动关闭连接");
            break;
        }
        else
        {
            // 接收错误
            int error_code = _show_socket_error_reason("client_recv_data", g_connect_socket);
            consecutive_errors++;
            
            //ESP_LOGE(TAG, "[数据接收] 接收数据错误，错误码: %d, 连续错误次数: %d", 
            //         error_code, consecutive_errors);
            
            // 只有在连续多次错误时才触发重连，避免偶发性网络问题导致的重连
            if (consecutive_errors >= 3)
            {
                //ESP_LOGE(TAG, "[数据接收] 连续接收错误过多，判断连接异常");
                break;
            }
            else
            {
                //ESP_LOGW(TAG, "[数据接收] 偶发性接收错误，继续尝试...");
                vTaskDelay(100 / portTICK_RATE_MS); // 短暂延时后重试
                continue;
            }
        }
    }
    
    //ESP_LOGW(TAG, "[数据接收] 接收任务结束 - 总接收包数: %d, 总字节数: %d", 
    //         recv_count, recv_bytes_total);
    
    // 接收任务异常退出，触发重连
    _close_socket();
    g_rxtx_need_restart = true;
    g_online_state = enNO;
    g_current_recv_task = NULL; // 清空任务句柄
    
    //ESP_LOGI(TAG, "[数据接收] 已触发重连机制");
    
    vTaskDelete(NULL);
}

// 获取socket错误代码
int _get_socket_error_code(int socket)
{
    int result = 0;
    u32_t optlen = sizeof(int);
    int err = getsockopt(socket, SOL_SOCKET, SO_ERROR, &result, &optlen);
    if (err == -1)
    {
        //ESP_LOGE(TAG, "[错误处理] 获取socket错误码失败: %s", strerror(err));
        return -1;
    }
    return result;
}

// 获取socket错误原因
int _show_socket_error_reason(const char *str, int socket)
{
    int err = _get_socket_error_code(socket);

    if (err != 0)
    {
        //ESP_LOGW(TAG, "[错误处理] %s socket错误 - 错误码: %d, 描述: %s", str, err, strerror(err));
    }

    return err;
}

// 关闭socket
void _close_socket()
{
    if (g_connect_socket > 0)
    {
        //ESP_LOGI(TAG, "[Socket管理] 关闭socket连接 fd: %d", g_connect_socket);
        close(g_connect_socket);
        g_connect_socket = -1;
    }
    else
    {
        //ESP_LOGD(TAG, "[Socket管理] Socket已经关闭或无效");
    }
}

// 添加连接状态检查函数，返回true表示连接有效
bool _check_socket_connection(int socket_fd)
{
    if (socket_fd <= 0)
    {
        return false;
    }
    
    // 使用MSG_PEEK标志检查socket状态，不消费数据
    char test_buf[1];
    int result = recv(socket_fd, test_buf, 1, MSG_PEEK | MSG_DONTWAIT);
    
    if (result == 0)
    {
        // 连接已被对端关闭
        //ESP_LOGW(TAG, "[连接检查] Socket连接已被服务器关闭");
        return false;
    }
    else if (result < 0)
    {
        int error_no = errno;
        if (error_no == EAGAIN || error_no == EWOULDBLOCK)
        {
            // 没有数据可读，但连接仍然有效
            return true;
        }
        else if (error_no == ECONNRESET || error_no == EPIPE || error_no == ENOTCONN)
        {
            // 连接已断开
            //ESP_LOGW(TAG, "[连接检查] Socket连接异常：%d (%s)", error_no, strerror(error_no));
            return false;
        }
        else
        {
            // 其他错误，假设连接仍然有效
            //ESP_LOGW(TAG, "[连接检查] Socket状态未知错误：%d (%s)", error_no, strerror(error_no));
            return true;
        }
    }
    else
    {
        // 有数据可读，连接有效
        return true;
    }
}



	
