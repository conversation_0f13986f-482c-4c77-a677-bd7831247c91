[{"children": [], "depends_on": null, "help": null, "id": "IDF_CMAKE", "name": "IDF_CMAKE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_ENV_FPGA", "name": "IDF_ENV_FPGA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ARCH_RISCV", "name": "IDF_TARGET_ARCH_RISCV", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ARCH_XTENSA", "name": "IDF_TARGET_ARCH_XTENSA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET", "name": "IDF_TARGET", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32", "name": "IDF_TARGET_ESP32", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32S2", "name": "IDF_TARGET_ESP32S2", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32S3", "name": "IDF_TARGET_ESP32S3", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32C3", "name": "IDF_TARGET_ESP32C3", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_ESP32H2", "name": "IDF_TARGET_ESP32H2", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_TARGET_LINUX", "name": "IDF_TARGET_LINUX", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "IDF_FIRMWARE_CHIP_ID", "name": "IDF_FIRMWARE_CHIP_ID", "range": null, "title": null, "type": "hex"}, {"children": [{"children": [], "depends_on": null, "help": "The prefix/path that is used to call the toolchain. The default setting assumes\na crosstool-ng gcc setup that is in your PATH.", "id": "SDK_TOOLPREFIX", "name": "SDK_TOOLPREFIX", "range": null, "title": "Compiler toolchain path/prefix", "type": "string"}, {"children": [], "depends_on": "!IDF_CMAKE", "help": "The executable name/path that is used to run python.\n\n(Note: This option is used with the legacy GNU Make build system only.)", "id": "SDK_PYTHON", "name": "SDK_PYTHON", "range": null, "title": "Python interpreter", "type": "string"}, {"children": [], "depends_on": "!IDF_CMAKE", "help": "Adds --warn-undefined-variables to MAKEFLAGS. This causes make to\nprint a warning any time an undefined variable is referenced.\n\nThis option helps find places where a variable reference is misspelled\nor otherwise missing, but it can be unwanted if you have Makefiles which\ndepend on undefined variables expanding to an empty string.\n\n(Note: this option is used with the legacy GNU Make build system only.)", "id": "SDK_MAKE_WARN_UNDEFINED_VARIABLES", "name": "SDK_MAKE_WARN_UNDEFINED_VARIABLES", "range": null, "title": "'make' warns on undefined variables", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this option in case you have a custom toolchain which supports time_t wide 64-bits.\nThis option checks time_t is 64-bits and disables ROM time functions\nto use the time functions from the toolchain instead.\nThis option allows resolving the Y2K38 problem.\nSee \"Setup Linux Toolchain from Scratch\" to build\na custom toolchain which supports 64-bits time_t.\n\nNote: ESP-IDF does not currently come with any pre-compiled toolchain\nthat supports 64-bit wide time_t.\nThis will change in a future major release,\nbut currently 64-bit time_t requires a custom built toolchain.", "id": "SDK_TOOLCHAIN_SUPPORTS_TIME_WIDE_64_BITS", "name": "SDK_TOOLCHAIN_SUPPORTS_TIME_WIDE_64_BITS", "range": null, "title": "Toolchain supports time_t wide 64-bits", "type": "bool"}], "depends_on": null, "id": "sdk-tool-configuration", "title": "SDK tool configuration", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice APP_BUILD_TYPE>", "help": null, "id": "APP_BUILD_TYPE_APP_2NDBOOT", "name": "APP_BUILD_TYPE_APP_2NDBOOT", "range": null, "title": "Default (binary application + 2nd stage bootloader)", "type": "bool"}, {"children": [], "depends_on": "<choice APP_BUILD_TYPE>", "help": null, "id": "APP_BUILD_TYPE_ELF_RAM", "name": "APP_BUILD_TYPE_ELF_RAM", "range": null, "title": "ELF file, loadable into RAM (EXPERIMENTAL))", "type": "bool"}], "depends_on": null, "help": "Select the way the application is built.\n\nBy default, the application is built as a binary file in a format compatible with\nthe ESP-IDF bootloader. In addition to this application, 2nd stage bootloader is\nalso built. Application and bootloader binaries can be written into flash and\nloaded/executed from there.\n\nAnother option, useful for only very small and limited applications, is to only link\nthe .elf file of the application, such that it can be loaded directly into RAM over\nJTAG. Note that since IRAM and DRAM sizes are very limited, it is not possible to\nbuild any complex application this way. However for kinds of testing and debugging,\nthis option may provide faster iterations, since the application does not need to be\nwritten into flash.\nNote that at the moment, ESP-IDF does not contain all the startup code required to\ninitialize the CPUs and ROM memory (data/bss). Therefore it is necessary to execute\na bit of ROM code prior to executing the application. A gdbinit file may look as follows (for ESP32):\n\n    # Connect to a running instance of OpenOCD\n    target remote :3333\n    # Reset and halt the target\n    mon reset halt\n    # Run to a specific point in ROM code,\n    #  where most of initialization is complete.\n    thb *0x40007d54\n    c\n    # Load the application into RAM\n    load\n    # Run till app_main\n    tb app_main\n    c\n\nExecute this gdbinit file as follows:\n\n    xtensa-esp32-elf-gdb build/app-name.elf -x gdbinit\n\nExample gdbinit files for other targets can be found in tools/test_apps/system/gdb_loadable_elf/\n\nRecommended sdkconfig.defaults for building loadable ELF files is as follows.\nCONFIG_APP_BUILD_TYPE_ELF_RAM is required, other options help reduce application\nmemory footprint.\n\n    CONFIG_APP_BUILD_TYPE_ELF_RAM=y\n    CONFIG_VFS_SUPPORT_TERMIOS=\n    CONFIG_NEWLIB_NANO_FORMAT=y\n    CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT=y\n    CONFIG_ESP_DEBUG_STUBS_ENABLE=\n    CONFIG_ESP_ERR_TO_NAME_LOOKUP=", "id": "build-type-application-build-type", "name": "APP_BUILD_TYPE", "title": "Application build type", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "APP_BUILD_GENERATE_BINARIES", "name": "APP_BUILD_GENERATE_BINARIES", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "APP_BUILD_BOOTLOADER", "name": "APP_BUILD_BOOTLOADER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "APP_BUILD_USE_FLASH_SECTIONS", "name": "APP_BUILD_USE_FLASH_SECTIONS", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "build-type", "title": "Build type", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "Offset address that 2nd bootloader will be flashed to.\nThe value is determined by the ROM bootloader.\nIt's not configurable in ESP-IDF.", "id": "BOOTLOADER_OFFSET_IN_FLASH", "name": "BOOTLOADER_OFFSET_IN_FLASH", "range": null, "title": null, "type": "hex"}, {"children": [{"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE", "range": null, "title": "Size (-Os)", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG", "range": null, "title": "Debug (-Og)", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_PERF", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_PERF", "range": null, "title": "Optimize for performance (-O2)", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_COMPILER_OPTIMIZATION>", "help": null, "id": "BOOTLOADER_COMPILER_OPTIMIZATION_NONE", "name": "BOOTLOADER_COMPILER_OPTIMIZATION_NONE", "range": null, "title": "Debug without optimization (-O0)", "type": "bool"}], "depends_on": null, "help": "This option sets compiler optimization level (gcc -O argument)\nfor the bootloader.\n\n- The default \"Size\" setting will add the -0s flag to CFLAGS.\n- The \"Debug\" setting will add the -Og flag to CFLAGS.\n- The \"Performance\" setting will add the -O2 flag to CFLAGS.\n- The \"None\" setting will add the -O0 flag to CFLAGS.\n\nNote that custom optimization levels may be unsupported.", "id": "bootloader-config-bootloader-optimization-level", "name": "BOOTLOADER_COMPILER_OPTIMIZATION", "title": "Bootloader optimization Level", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_NONE", "name": "BOOTLOADER_LOG_LEVEL_NONE", "range": null, "title": "No output", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_ERROR", "name": "BOOTLOADER_LOG_LEVEL_ERROR", "range": null, "title": "Error", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_WARN", "name": "BOOTLOADER_LOG_LEVEL_WARN", "range": null, "title": "Warning", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_INFO", "name": "BOOTLOADER_LOG_LEVEL_INFO", "range": null, "title": "Info", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_DEBUG", "name": "BOOTLOADER_LOG_LEVEL_DEBUG", "range": null, "title": "Debug", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_LOG_LEVEL>", "help": null, "id": "BOOTLOADER_LOG_LEVEL_VERBOSE", "name": "BOOTLOADER_LOG_LEVEL_VERBOSE", "range": null, "title": "Verbose", "type": "bool"}], "depends_on": null, "help": "Specify how much output to see in bootloader logs.", "id": "bootloader-config-bootloader-log-verbosity", "name": "BOOTLOADER_LOG_LEVEL", "title": "Bootloader log verbosity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "BOOTLOADER_LOG_LEVEL", "name": "BOOTLOADER_LOG_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "IDF_TARGET_ESP32 && (ESPTOOLPY_FLASHMODE_QIO || ESPTOOLPY_FLASHMODE_QOUT)", "help": "This setting is only used if the SPI flash pins have been overridden by setting the eFuses\nSPI_PAD_CONFIG_xxx, and the SPI flash mode is QIO or QOUT.\n\nWhen this is the case, the eFuse config only defines 3 of the 4 Quad I/O data pins. The WP pin (aka\nESP32 pin \"SD_DATA_3\" or SPI flash pin \"IO2\") is not specified in eFuse. The same pin is also used\nfor external SPIRAM if it is enabled.\n\nIf this config item is set to N (default), the correct WP pin will be automatically used for any\nEspressif chip or module with integrated flash. If a custom setting is needed, set this config item to\nY and specify the GPIO number connected to the WP.", "id": "BOOTLOADER_SPI_CUSTOM_WP_PIN", "name": "BOOTLOADER_SPI_CUSTOM_WP_PIN", "range": null, "title": "Use custom SPI Flash WP Pin when flash pins set in eFuse (read help)", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32 && (ESPTOOLPY_FLASHMODE_QIO || ESPTOOLPY_FLASHMODE_QOUT)", "help": "The option \"Use custom SPI Flash WP Pin\" must be set or this value is ignored\n\nIf burning a customized set of SPI flash pins in eFuse and using QIO or QOUT mode for flash, set this\nvalue to the GPIO number of the SPI flash WP pin.", "id": "BOOTLOADER_SPI_WP_PIN", "name": "BOOTLOADER_SPI_WP_PIN", "range": null, "title": "Custom SPI Flash WP Pin", "type": "int"}, {"children": [{"children": [], "depends_on": "!ESPTOOLPY_FLASHFREQ_80M && <choice BOOTLOADER_VDDSDIO_BOOST>", "help": null, "id": "BOOTLOADER_VDDSDIO_BOOST_1_8V", "name": "BOOTLOADER_VDDSDIO_BOOST_1_8V", "range": null, "title": "1.8V", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_VDDSDIO_BOOST>", "help": null, "id": "BOOTLOADER_VDDSDIO_BOOST_1_9V", "name": "BOOTLOADER_VDDSDIO_BOOST_1_9V", "range": null, "title": "1.9V", "type": "bool"}], "depends_on": null, "help": "If this option is enabled, and VDDSDIO LDO is set to 1.8V (using eFuse\nor MTDI bootstrapping pin), bootloader will change LDO settings to\noutput 1.9V instead. This helps prevent flash chip from browning out\nduring flash programming operations.\n\nThis option has no effect if VDDSDIO is set to 3.3V, or if the internal\nVDDSDIO regulator is disabled via eFuse.", "id": "bootloader-config-vddsdio-ldo-voltage", "name": "BOOTLOADER_VDDSDIO_BOOST", "title": "VDDSDIO LDO voltage", "type": "choice"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "The selected GPIO will be configured as an input with internal pull-up enabled (note that on some SoCs.\nnot all pins have an internal pull-up, consult the hardware datasheet for details.) To trigger a factory\nreset, this GPIO must be held high or low (as configured) on startup.", "id": "BOOTLOADER_NUM_PIN_FACTORY_RESET", "name": "BOOTLOADER_NUM_PIN_FACTORY_RESET", "range": null, "title": "Number of the GPIO input for factory reset", "type": "int"}, {"children": [{"children": [], "depends_on": "<choice BOOTLOADER_FACTORY_RESET_PIN_LEVEL>", "help": null, "id": "BOOTLOADER_FACTORY_RESET_PIN_LOW", "name": "BOOTLOADER_FACTORY_RESET_PIN_LOW", "range": null, "title": "Reset on GPIO low", "type": "bool"}, {"children": [], "depends_on": "<choice BOOTLOADER_FACTORY_RESET_PIN_LEVEL>", "help": null, "id": "BOOTLOADER_FACTORY_RESET_PIN_HIGH", "name": "BOOTLOADER_FACTORY_RESET_PIN_HIGH", "range": null, "title": "Reset on GPIO high", "type": "bool"}], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "Pin level for factory reset, can be triggered on low or high.", "id": "bootloader-config-gpio-triggers-factory-reset-factory-reset-gpio-level", "name": "BOOTLOADER_FACTORY_RESET_PIN_LEVEL", "title": "Factory reset GPIO level", "type": "choice"}, {"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "The device will boot from \"factory\" partition (or OTA slot 0 if no factory partition is present) after a\nfactory reset.", "id": "BOOTLOADER_OTA_DATA_ERASE", "name": "BOOTLOADER_OTA_DATA_ERASE", "range": null, "title": "Clear OTA data on factory reset (select factory partition)", "type": "bool"}, {"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET", "help": "Allows customers to select which data partitions will be erased while factory reset.\n\nSpecify the names of partitions as a comma-delimited with optional spaces for readability. (Like this:\n\"nvs, phy_init, ...\")\nMake sure that the name specified in the partition table and here are the same.\nPartitions of type \"app\" cannot be specified here.", "id": "BOOTLOADER_DATA_FACTORY_RESET", "name": "BOOTLOADER_DATA_FACTORY_RESET", "range": null, "title": "Comma-separated names of partitions to clear on factory reset", "type": "string"}], "depends_on": null, "help": "Allows to reset the device to factory settings:\n- clear one or more data partitions;\n- boot from \"factory\" partition.\nThe factory reset will occur if there is a GPIO input held at the configured level while\ndevice starts up. See settings below.", "id": "BOOTLOADER_FACTORY_RESET", "name": "BOOTLOADER_FACTORY_RESET", "range": null, "title": "GPIO triggers factory reset", "type": "bool"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_APP_TEST", "help": "The selected GPIO will be configured as an input with internal pull-up enabled.\nTo trigger a test app, this GPIO must be pulled low on reset.\nAfter the GPIO input is deactivated and the device reboots, the old application will boot.\n(factory or OTA[x]).\nNote that GPIO34-39 do not have an internal pullup and an external one must be provided.", "id": "BOOTLOADER_NUM_PIN_APP_TEST", "name": "BOOTLOADER_NUM_PIN_APP_TEST", "range": null, "title": "Number of the GPIO input to boot TEST partition", "type": "int"}], "depends_on": "!BOOTLOADER_APP_ANTI_ROLLBACK", "help": "Allows to run the test app from \"TEST\" partition.\nA boot from \"test\" partition will occur if there is a GPIO input pulled low while device starts up.\nSee settings below.", "id": "BOOTLOADER_APP_TEST", "name": "BOOTLOADER_APP_TEST", "range": null, "title": "GPIO triggers boot from test app partition", "type": "bool"}, {"children": [], "depends_on": "BOOTLOADER_FACTORY_RESET || BOOTLOADER_APP_TEST", "help": "The GPIO must be held low continuously for this period of time after reset\nbefore a factory reset or test partition boot (as applicable) is performed.", "id": "BOOTLOADER_HOLD_TIME_GPIO", "name": "BOOTLOADER_HOLD_TIME_GPIO", "range": null, "title": "Hold time of GPIO for reset/test mode (seconds)", "type": "int"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_WDT_ENABLE", "help": "If this option is set, the ESP-IDF app must explicitly reset, feed, or disable the rtc_wdt in\nthe app's own code.\nIf this option is not set (default), then rtc_wdt will be disabled by ESP-IDF before calling\nthe app_main() function.\n\nUse function rtc_wdt_feed() for resetting counter of rtc_wdt.\nUse function rtc_wdt_disable() for disabling rtc_wdt.", "id": "BOOTLOADER_WDT_DISABLE_IN_USER_CODE", "name": "BOOTLOADER_WDT_DISABLE_IN_USER_CODE", "range": null, "title": "Allows RTC watchdog disable in user code", "type": "bool"}, {"children": [], "depends_on": "BOOTLOADER_WDT_ENABLE", "help": "Verify that this parameter is correct and more then the execution time.\nPay attention to options such as reset to factory, trigger test partition and encryption on boot\n- these options can increase the execution time.\nNote: RTC_WDT will reset while encryption operations will be performed.", "id": "BOOTLOADER_WDT_TIME_MS", "name": "BOOTLOADER_WDT_TIME_MS", "range": [0, 120000], "title": "Timeout for RTC watchdog (ms)", "type": "int"}], "depends_on": null, "help": "Tracks the execution time of startup code.\nIf the execution time is exceeded, the RTC_WDT will restart system.\nIt is also useful to prevent a lock up in start code caused by an unstable power source.\nNOTE: Tracks the execution time starts from the bootloader code - re-set timeout, while selecting the\nsource for slow_clk - and ends calling app_main.\nRe-set timeout is needed due to WDT uses a SLOW_CLK clock source. After changing a frequency slow_clk a\ntime of WDT needs to re-set for new frequency.\nslow_clk depends on ESP32_RTC_CLK_SRC (INTERNAL_RC or EXTERNAL_CRYSTAL).", "id": "BOOTLOADER_WDT_ENABLE", "name": "BOOTLOADER_WDT_ENABLE", "range": null, "title": "Use RTC watchdog in start code", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "BOOTLOADER_APP_ANTI_ROLLBACK", "help": "The secure version is the sequence number stored in the header of each firmware.\nThe security version is set in the bootloader, version is recorded in the eFuse field\nas the number of set ones. The allocated number of bits in the efuse field\nfor storing the security version is limited (see BOOTLOADER_APP_SEC_VER_SIZE_EFUSE_FIELD option).\n\nBootloader: When bootloader selects an app to boot, an app is selected that has\na security version greater or equal that recorded in eFuse field.\nThe app is booted with a higher (or equal) secure version.\n\nThe security version is worth increasing if in previous versions there is\na significant vulnerability and their use is not acceptable.\n\nYour partition table should has a scheme with ota_0 + ota_1 (without factory).", "id": "BOOTLOADER_APP_SECURE_VERSION", "name": "BOOTLOADER_APP_SECURE_VERSION", "range": null, "title": "eFuse secure version of app", "type": "int"}, {"children": [], "depends_on": "BOOTLOADER_APP_ANTI_ROLLBACK", "help": "The size of the efuse secure version field.\nIts length is limited to 32 bits for ESP32 and 16 bits for ESP32-S2.\nThis determines how many times the security version can be increased.", "id": "BOOTLOADER_APP_SEC_VER_SIZE_EFUSE_FIELD", "name": "BOOTLOADER_APP_SEC_VER_SIZE_EFUSE_FIELD", "range": null, "title": "Size of the efuse secure version field", "type": "int"}, {"children": [], "depends_on": "BOOTLOADER_APP_ANTI_ROLLBACK", "help": "This option allows to emulate read/write operations with all eFuses and efuse secure version.\nIt allows to test anti-rollback implemention without permanent write eFuse bits.\nThere should be an entry in partition table with following details: `emul_efuse, data, efuse, , 0x2000`.\n\nThis option enables: EFUSE_VIRTUAL and EFUSE_VIRTUAL_KEEP_IN_FLASH.", "id": "BOOTLOADER_EFUSE_SECURE_VERSION_EMULATE", "name": "BOOTLOADER_EFUSE_SECURE_VERSION_EMULATE", "range": null, "title": "Emulate operations with efuse secure version(only test)", "type": "bool"}], "depends_on": "BOOTLOADER_APP_ROLLBACK_ENABLE", "help": "This option prevents rollback to previous firmware/application image with lower security version.", "id": "BOOTLOADER_APP_ANTI_ROLLBACK", "name": "BOOTLOADER_APP_ANTI_ROLLBACK", "range": null, "title": "Enable app anti-rollback support", "type": "bool"}], "depends_on": null, "help": "After updating the app, the bootloader runs a new app with the \"ESP_OTA_IMG_PENDING_VERIFY\" state set.\nThis state prevents the re-run of this app. After the first boot of the new app in the user code, the\nfunction should be called to confirm the operability of the app or vice versa about its non-operability.\nIf the app is working, then it is marked as valid. Otherwise, it is marked as not valid and rolls back to\nthe previous working app. A reboot is performed, and the app is booted before the software update.\nNote: If during the first boot a new app the power goes out or the WDT works, then roll back will happen.\nRollback is possible only between the apps with the same security versions.", "id": "BOOTLOADER_APP_ROLLBACK_ENABLE", "name": "BOOTLOADER_APP_ROLLBACK_ENABLE", "range": null, "title": "Enable app rollback support", "type": "bool"}, {"children": [], "depends_on": "(SECURE_BOOT && SECURE_BOOT_INSECURE) || !SECURE_BOOT", "help": "This option disables the normal validation of an image coming out of\ndeep sleep (checksums, SHA256, and signature). This is a trade-off\nbetween wakeup performance from deep sleep, and image integrity checks.\n\nOnly enable this if you know what you are doing. It should not be used\nin conjunction with using deep_sleep() entry and changing the active OTA\npartition as this would skip the validation upon first load of the new\nOTA partition.\n\nIt is possible to enable this option with Secure Boot if \"allow insecure\noptions\" is enabled, however it's strongly recommended to NOT enable it as\nit may allow a Secure Boot bypass.", "id": "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP", "name": "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP", "range": null, "title": "Skip image validation when exiting deep sleep", "type": "bool"}, {"children": [], "depends_on": "!SECURE_SIGNED_ON_BOOT", "help": "Some applications need to boot very quickly from power on. By default, the entire app binary\nis read from flash and verified which takes up a significant portion of the boot time.\n\nEnabling this option will skip validation of the app when the SoC boots from power on.\nNote that in this case it's not possible for the bootloader to detect if an app image is\ncorrupted in the flash, therefore it's not possible to safely fall back to a different app\npartition. Flash corruption of this kind is unlikely but can happen if there is a serious\nfirmware bug or physical damage.\n\nFollowing other reset types, the bootloader will still validate the app image. This increases\nthe chances that flash corruption resulting in a crash can be detected following soft reset, and\nthe bootloader will fall back to a valid app image. To increase the chances of successfully recovering\nfrom a flash corruption event, keep the option BOOTLOADER_WDT_ENABLE enabled and consider also enabling\nBOOTLOADER_WDT_DISABLE_IN_USER_CODE - then manually disable the RTC Watchdog once the app is running.\nIn addition, enable both the Task and Interrupt watchdog timers with reset options set.", "id": "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON", "name": "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON", "range": null, "title": "Skip image validation from power on reset (READ HELP FIRST)", "type": "bool"}, {"children": [], "depends_on": "!SECURE_SIGNED_ON_BOOT", "help": "Selecting this option prevents the bootloader from ever validating the app image before\nbooting it. Any flash corruption of the selected app partition will make the entire SoC\nunbootable.\n\nAlthough flash corruption is a very rare case, it is not recommended to select this option.\nConsider selecting \"Skip image validation from power on reset\" instead. However, if boot time\nis the only important factor then it can be enabled.", "id": "BOOTLOADER_SKIP_VALIDATE_ALWAYS", "name": "BOOTLOADER_SKIP_VALIDATE_ALWAYS", "range": null, "title": "Skip image validation always (READ HELP FIRST)", "type": "bool"}, {"children": [], "depends_on": null, "help": "Reserve RTC FAST memory for Skip image validation. This option in bytes.\nThis option reserves an area in the RTC FAST memory (access only PRO_CPU).\nUsed to save the addresses of the selected application.\nWhen a wakeup occurs (from Deep sleep), the bootloader retrieves it and\nloads the application without validation.", "id": "BOOTLOADER_RESERVE_RTC_SIZE", "name": "BOOTLOADER_RESERVE_RTC_SIZE", "range": null, "title": null, "type": "hex"}, {"children": [{"children": [], "depends_on": "BOOTLOADER_CUSTOM_RESERVE_RTC", "help": "This option reserves in RTC FAST memory the area for custom purposes.\nIf you want to create your own bootloader and save more information\nin this area of memory, you can increase it. It must be a multiple of 4 bytes.\nThis area (rtc_retain_mem_t) is reserved and has access from the bootloader and an application.", "id": "BOOTLOADER_CUSTOM_RESERVE_RTC_SIZE", "name": "BOOTLOADER_CUSTOM_RESERVE_RTC_SIZE", "range": null, "title": "Size in bytes for custom purposes", "type": "hex"}], "depends_on": null, "help": "This option allows the customer to place data in the RTC FAST memory,\nthis area remains valid when rebooted, except for power loss.\nThis memory is located at a fixed address and is available\nfor both the bootloader and the application.\n(The application and bootoloader must be compiled with the same option).\nThe RTC FAST memory has access only through PRO_CPU.", "id": "BOOTLOADER_CUSTOM_RESERVE_RTC", "name": "BOOTLOADER_CUSTOM_RESERVE_RTC", "range": null, "title": "Reserve RTC FAST memory for custom purposes", "type": "bool"}, {"children": [], "depends_on": null, "help": "Perform the startup flow recommended by XMC. Please consult XMC for the details of this flow.\nXMC chips will be forbidden to be used, when this option is disabled.\n\nDON'T DISABLE THIS UNLESS YOU KNOW WHAT YOU ARE DOING.", "id": "BOOTLOADER_FLASH_XMC_SUPPORT", "name": "BOOTLOADER_FLASH_XMC_SUPPORT", "range": null, "title": "Enable the support for flash chips of XMC (READ HELP FIRST)", "type": "bool"}], "depends_on": null, "id": "bootloader-config", "title": "Bootloader config", "type": "menu"}, {"children": [{"children": [], "depends_on": "SECURE_BOOT || SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT", "help": null, "id": "SECURE_SIGNED_ON_BOOT", "name": "SECURE_SIGNED_ON_BOOT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT || SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT", "help": null, "id": "SECURE_SIGNED_ON_UPDATE", "name": "SECURE_SIGNED_ON_UPDATE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "SECURE_SIGNED_ON_BOOT || SECURE_SIGNED_ON_UPDATE", "help": null, "id": "SECURE_SIGNED_APPS", "name": "SECURE_SIGNED_APPS", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "ESP32_REV_MIN_3 || IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3", "help": null, "id": "SECURE_BOOT_SUPPORTS_RSA", "name": "SECURE_BOOT_SUPPORTS_RSA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3", "help": null, "id": "SECURE_TARGET_HAS_SECURE_ROM_DL_MODE", "name": "SECURE_TARGET_HAS_SECURE_ROM_DL_MODE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "!SECURE_BOOT", "help": "Require apps to be signed to verify their integrity.\n\nThis option uses the same app signature scheme as hardware secure boot, but unlike hardware secure boot it\ndoes not prevent the bootloader from being physically updated. This means that the device can be secured\nagainst remote network access, but not physical access. Compared to using hardware Secure Boot this option\nis much simpler to implement.", "id": "SECURE_SIGNED_APPS_NO_SECURE_BOOT", "name": "SECURE_SIGNED_APPS_NO_SECURE_BOOT", "range": null, "title": "Require signed app images", "type": "bool"}, {"children": [{"children": [], "depends_on": "IDF_TARGET_ESP32 && (SECURE_SIGNED_APPS_NO_SECURE_BOOT || SECURE_BOOT_V1_ENABLED) && <choice SECURE_SIGNED_APPS_SCHEME>", "help": "Embeds the ECDSA public key in the bootloader and signs the application with an ECDSA key.\n\nRefer to the documentation before enabling.", "id": "SECURE_SIGNED_APPS_ECDSA_SCHEME", "name": "SECURE_SIGNED_APPS_ECDSA_SCHEME", "range": null, "title": "ECDSA", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_SUPPORTS_RSA && (SECURE_SIGNED_APPS_NO_SECURE_BOOT || SECURE_BOOT_V2_ENABLED) && <choice SECURE_SIGNED_APPS_SCHEME>", "help": "Appends the RSA-3072 based Signature block to the application.\nRefer to <Secure Boot Version 2 documentation link> before enabling.", "id": "SECURE_SIGNED_APPS_RSA_SCHEME", "name": "SECURE_SIGNED_APPS_RSA_SCHEME", "range": null, "title": "RSA", "type": "bool"}], "depends_on": "SECURE_BOOT || SECURE_SIGNED_APPS_NO_SECURE_BOOT", "help": "Select the Secure App signing scheme. Depends on the Chip Revision.\nThere are two options:\n1. ECDSA based secure boot scheme. (Only choice for Secure Boot V1)\nSupported in ESP32 and ESP32-ECO3.\n2. The RSA based secure boot scheme. (Only choice for Secure Boot V2)\nSupported in ESP32-ECO3 (ESP32 Chip Revision 3 onwards), ESP32-S2, ESP32-C3, ESP32-S3.", "id": "security-features-app-signing-scheme", "name": "SECURE_SIGNED_APPS_SCHEME", "title": "App Signing Scheme", "type": "choice"}, {"children": [], "depends_on": "SECURE_SIGNED_APPS_NO_SECURE_BOOT && SECURE_SIGNED_APPS_ECDSA_SCHEME", "help": "If this option is set, the bootloader will be compiled with code to verify that an app is signed before\nbooting it.\n\nIf hardware secure boot is enabled, this option is always enabled and cannot be disabled.\nIf hardware secure boot is not enabled, this option doesn't add significant security by itself so most\nusers will want to leave it disabled.", "id": "SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT", "name": "SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT", "range": null, "title": "Bootloader verifies app signatures", "type": "bool"}, {"children": [], "depends_on": "SECURE_SIGNED_APPS_NO_SECURE_BOOT", "help": "If this option is set, any OTA updated apps will have the signature verified before being considered valid.\n\nWhen enabled, the signature is automatically checked whenever the esp_ota_ops.h APIs are used for OTA\nupdates, or esp_image_format.h APIs are used to verify apps.\n\nIf hardware secure boot is enabled, this option is always enabled and cannot be disabled.\nIf hardware secure boot is not enabled, this option still adds significant security against network-based\nattackers by preventing spoofing of OTA updates.", "id": "SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT", "name": "SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT", "range": null, "title": "Verify app signature on update", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "IDF_TARGET_ESP32 && <choice SECURE_BOOT_VERSION>", "help": "Build a bootloader which enables secure boot version 1 on first boot.\nRefer to the Secure Boot section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_V1_ENABLED", "name": "SECURE_BOOT_V1_ENABLED", "range": null, "title": "Enable Secure Boot version 1", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_SUPPORTS_RSA && <choice SECURE_BOOT_VERSION>", "help": "Build a bootloader which enables Secure Boot version 2 on first boot.\nRefer to Secure Boot V2 section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_V2_ENABLED", "name": "SECURE_BOOT_V2_ENABLED", "range": null, "title": "Enable Secure Boot version 2", "type": "bool"}], "depends_on": "SECURE_BOOT", "help": "Select the Secure Boot Version. Depends on the Chip Revision.\nSecure Boot V2 is the new RSA based secure boot scheme.\nSupported in ESP32-ECO3 (ESP32 Chip Revision 3 onwards), ESP32-S2, ESP32-C3 ECO3.\nSecure Boot V1 is the AES based secure boot scheme.\nSupported in ESP32 and ESP32-ECO3.", "id": "security-features-enable-hardware-secure-boot-in-bootloader-read-docs-first--select-secure-boot-version", "name": "SECURE_BOOT_VERSION", "title": "Select secure boot version", "type": "choice"}], "depends_on": "IDF_TARGET_ESP32 || IDF_TARGET_ESP32S2 || ESP32C3_REV_MIN_3 || IDF_TARGET_ESP32S3", "help": "Build a bootloader which enables Secure Boot on first boot.\n\nOnce enabled, Secure Boot will not boot a modified bootloader. The bootloader will only load a partition\ntable or boot an app if the data has a verified digital signature. There are implications for reflashing\nupdated apps once secure boot is enabled.\n\nWhen enabling secure boot, JTAG and ROM BASIC Interpreter are permanently disabled by default.", "id": "SECURE_BOOT", "name": "SECURE_BOOT", "range": null, "title": "Enable hardware Secure Boot in bootloader (READ DOCS FIRST)", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SECURE_BOOTLOADER_MODE>", "help": "On first boot, the bootloader will generate a key which is not readable externally or by software. A\ndigest is generated from the bootloader image itself. This digest will be verified on each subsequent\nboot.\n\nEnabling this option means that the bootloader cannot be changed after the first time it is booted.", "id": "SECURE_BOOTLOADER_ONE_TIME_FLASH", "name": "SECURE_BOOTLOADER_ONE_TIME_FLASH", "range": null, "title": "One-time flash", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_BOOTLOADER_MODE>", "help": "Generate a reusable secure bootloader key, derived (via SHA-256) from the secure boot signing key.\n\nThis allows the secure bootloader to be re-flashed by anyone with access to the secure boot signing\nkey.\n\nThis option is less secure than one-time flash, because a leak of the digest key from one device\nallows reflashing of any device that uses it.", "id": "SECURE_BOOTLOADER_REFLASHABLE", "name": "SECURE_BOOTLOADER_REFLASHABLE", "range": null, "title": "Reflashable", "type": "bool"}], "depends_on": "SECURE_BOOT_V1_ENABLED", "help": null, "id": "security-features-secure-bootloader-mode", "name": "SECURE_BOOTLOADER_MODE", "title": "Secure bootloader mode", "type": "choice"}, {"children": [{"children": [], "depends_on": "SECURE_BOOT_BUILD_SIGNED_BIN<PERSON>IES", "help": "Path to the key file used to sign app images.\n\nKey file is an ECDSA private key (NIST256p curve) in PEM format for Secure Boot V1.\nKey file is an RSA private key in PEM format for Secure Boot V2.\n\nPath is evaluated relative to the project directory.\n\nYou can generate a new signing key by running the following command:\nespsecure.py generate_signing_key secure_boot_signing_key.pem\n\nSee the Secure Boot section of the ESP-IDF Programmer's Guide for this version for details.", "id": "SECURE_BOOT_SIGNING_KEY", "name": "SECURE_BOOT_SIGNING_KEY", "range": null, "title": "Secure boot private signing key", "type": "string"}], "depends_on": "SECURE_SIGNED_APPS", "help": "Once secure boot or signed app requirement is enabled, app images are required to be signed.\n\nIf enabled (default), these binary files are signed as part of the build process. The file named in\n\"Secure boot private signing key\" will be used to sign the image.\n\nIf disabled, unsigned app/partition data will be built. They must be signed manually using espsecure.py.\nVersion 1 to enable ECDSA Based Secure Boot and Version 2 to enable RSA based Secure Boot.\n(for example, on a remote signing server.)", "id": "SECURE_BOOT_BUILD_SIGNED_BIN<PERSON>IES", "name": "SECURE_BOOT_BUILD_SIGNED_BIN<PERSON>IES", "range": null, "title": "Sign binaries during build", "type": "bool"}, {"children": [], "depends_on": "SECURE_SIGNED_APPS && !SECURE_BOOT_BUILD_SIGNED_BINARIES && !SECURE_SIGNED_APPS_RSA_SCHEME", "help": "Path to a public key file used to verify signed images.\nSecure Boot V1: This ECDSA public key is compiled into the bootloader and/or\napp, to verify app images.\nSecure Boot V2: This RSA public key is compiled into the signature block at\nthe end of the bootloader/app.\n\nKey file is in raw binary format, and can be extracted from a\nPEM formatted private key using the espsecure.py\nextract_public_key command.\n\nRefer to the Secure Boot section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_VERIFICATION_KEY", "name": "SECURE_BOOT_VERIFICATION_KEY", "range": null, "title": "Secure boot public signature verification key", "type": "string"}, {"children": [], "depends_on": "SECURE_BOOT && (IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3)", "help": "If this option is set, ROM bootloader will revoke the public key digest burned in efuse block\nif it fails to verify the signature of software bootloader with it.\nRevocation of keys does not happen when enabling secure boot. Once secure boot is enabled,\nkey revocation checks will be done on subsequent boot-up, while verifying the software bootloader\n\nThis feature provides a strong resistance against physical attacks on the device.\n\nNOTE: Once a digest slot is revoked, it can never be used again to verify an image\nThis can lead to permanent bricking of the device, in case all keys are revoked\nbecause of signature verification failure.", "id": "SECURE_BOOT_ENABLE_AGGRESSIVE_KEY_REVOKE", "name": "SECURE_BOOT_ENABLE_AGGRESSIVE_KEY_REVOKE", "range": null, "title": "Enable Aggressive key revoke strategy", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SECURE_BOOTLOADER_KEY_ENCODING>", "help": null, "id": "SECURE_BOOTLOADER_KEY_ENCODING_256BIT", "name": "SECURE_BOOTLOADER_KEY_ENCODING_256BIT", "range": null, "title": "No encoding (256 bit key)", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_BOOTLOADER_KEY_ENCODING>", "help": null, "id": "SECURE_BOOTLOADER_KEY_ENCODING_192BIT", "name": "SECURE_BOOTLOADER_KEY_ENCODING_192BIT", "range": null, "title": "3/4 encoding (192 bit key)", "type": "bool"}], "depends_on": "SECURE_BOOTLOADER_REFLASHABLE", "help": "In reflashable secure bootloader mode, a hardware key is derived from the signing key (with SHA-256) and\ncan be written to eFuse with espefuse.py.\n\nNormally this is a 256-bit key, but if 3/4 Coding Scheme is used on the device then the eFuse key is\ntruncated to 192 bits.\n\nThis configuration item doesn't change any firmware code, it only changes the size of key binary which is\ngenerated at build time.", "id": "security-features-hardware-key-encoding", "name": "SECURE_BOOTLOADER_KEY_ENCODING", "title": "Hardware Key Encoding", "type": "choice"}, {"children": [], "depends_on": "SECURE_BOOT", "help": "You can disable some of the default protections offered by secure boot, in order to enable testing or a\ncustom combination of security features.\n\nOnly enable these options if you are very sure.\n\nRefer to the Secure Boot section of the ESP-IDF Programmer's Guide for this version before enabling.", "id": "SECURE_BOOT_INSECURE", "name": "SECURE_BOOT_INSECURE", "range": null, "title": "Allow potentially insecure options", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "<choice SECURE_FLASH_ENCRYPTION_KEYSIZE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_AES128", "name": "SECURE_FLASH_ENCRYPTION_AES128", "range": null, "title": "AES-128 (256-bit key)", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_FLASH_ENCRYPTION_KEYSIZE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_AES256", "name": "SECURE_FLASH_ENCRYPTION_AES256", "range": null, "title": "AES-256 (512-bit key)", "type": "bool"}], "depends_on": "(IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3) && SECURE_FLASH_ENC_ENABLED", "help": "Size of generated AES-XTS key.\n\nAES-128 uses a 256-bit key (32 bytes) which occupies one Efuse key block.\nAES-256 uses a 512-bit key (64 bytes) which occupies two Efuse key blocks.\n\nThis setting is ignored if either type of key is already burned to Efuse before the first boot.\nIn this case, the pre-burned key is used and no new key is generated.", "id": "security-features-enable-flash-encryption-on-boot-read-docs-first--size-of-generated-aes-xts-key", "name": "SECURE_FLASH_ENCRYPTION_KEYSIZE", "title": "Size of generated AES-XTS key", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice SECURE_FLASH_ENCRYPTION_MODE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "name": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "range": null, "title": "Development (NOT SECURE)", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_FLASH_ENCRYPTION_MODE>", "help": null, "id": "SECURE_FLASH_ENCRYPTION_MODE_RELEASE", "name": "SECURE_FLASH_ENCRYPTION_MODE_RELEASE", "range": null, "title": "Release", "type": "bool"}], "depends_on": "SECURE_FLASH_ENC_ENABLED", "help": "By default Development mode is enabled which allows ROM download mode to perform flash encryption\noperations (plaintext is sent to the device, and it encrypts it internally and writes ciphertext\nto flash.) This mode is not secure, it's possible for an attacker to write their own chosen plaintext\nto flash.\n\nRelease mode should always be selected for production or manufacturing. Once enabled it's no longer\npossible for the device in ROM Download Mode to use the flash encryption hardware.\n\nRefer to the Flash Encryption section of the ESP-IDF Programmer's Guide for details.", "id": "security-features-enable-flash-encryption-on-boot-read-docs-first--enable-usage-mode", "name": "SECURE_FLASH_ENCRYPTION_MODE", "title": "Enable usage mode", "type": "choice"}], "depends_on": null, "help": "If this option is set, flash contents will be encrypted by the bootloader on first boot.\n\nNote: After first boot, the system will be permanently encrypted. Re-flashing an encrypted\nsystem is complicated and not always possible.\n\nRead https://docs.espressif.com/projects/esp-idf/en/latest/security/flash-encryption.html\nbefore enabling.", "id": "SECURE_FLASH_ENC_ENABLED", "name": "SECURE_FLASH_ENC_ENABLED", "range": null, "title": "Enable flash encryption on boot (READ DOCS FIRST)", "type": "bool"}, {"children": [{"children": [], "depends_on": "(SECURE_BOOT_INSECURE || SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT) && IDF_TARGET_ESP32", "help": "By default, the BASIC ROM Console starts on reset if no valid bootloader is\nread from the flash.\n\nWhen either flash encryption or secure boot are enabled, the default is to\ndisable this BASIC fallback mode permanently via eFuse.\n\nIf this option is set, this eFuse is not burned and the BASIC ROM Console may\nremain accessible.  Only set this option in testing environments.", "id": "SECURE_BOOT_ALLOW_ROM_BASIC", "name": "SECURE_BOOT_ALLOW_ROM_BASIC", "range": null, "title": "Leave ROM BASIC Interpreter available on reset", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE || SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "help": "If not set (default), the bootloader will permanently disable JTAG (across entire chip) on first boot\nwhen either secure boot or flash encryption is enabled.\n\nSetting this option leaves JTAG on for debugging, which negates all protections of flash encryption\nand some of the protections of secure boot.\n\nOnly set this option in testing environments.", "id": "SECURE_BOOT_ALLOW_JTAG", "name": "SECURE_BOOT_ALLOW_JTAG", "range": null, "title": "Allow JTAG Debugging", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE", "help": "If not set (default), app partition size must be a multiple of 64KB. App images are padded to 64KB\nlength, and the bootloader checks any trailing bytes after the signature (before the next 64KB\nboundary) have not been written. This is because flash cache maps entire 64KB pages into the address\nspace. This prevents an attacker from appending unverified data after the app image in the flash,\ncausing it to be mapped into the address space.\n\nSetting this option allows the app partition length to be unaligned, and disables padding of the app\nimage to this length. It is generally not recommended to set this option, unless you have a legacy\npartitioning scheme which doesn't support 64KB aligned partition lengths.", "id": "SECURE_BOOT_ALLOW_SHORT_APP_PARTITION", "name": "SECURE_BOOT_ALLOW_SHORT_APP_PARTITION", "range": null, "title": "Allow app partition length not 64KB aligned", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE && SECURE_BOOT_V2_ENABLED", "help": "If not set (default, recommended), on first boot the bootloader will burn the WR_DIS_RD_DIS\nefuse when Secure Boot is enabled. This prevents any more efuses from being read protected.\n\nIf this option is set, it will remain possible to write the EFUSE_RD_DIS efuse field after Secure\nBoot is enabled. This may allow an attacker to read-protect the BLK2 efuse (for ESP32) and\nBLOCK4-BLOCK10 (i.e. BLOCK_KEY0-BLOCK_KEY5)(for other chips) holding the public key digest, causing an\nimmediate denial of service and possibly allowing an additional fault injection attack to\nbypass the signature protection.\n\nNOTE: Once a BLOCK is read-protected, the application will read all zeros from that block\n\nNOTE: If \"UART ROM download mode (Permanently disabled (recommended))\" or\n\"UART ROM download mode (Permanently switch to Secure mode (recommended))\" is set,\nthen it is __NOT__ possible to read/write efuses using espefuse.py utility.\nHowever, efuse can be read/written from the application", "id": "SECURE_BOOT_V2_ALLOW_EFUSE_RD_DIS", "name": "SECURE_BOOT_V2_ALLOW_EFUSE_RD_DIS", "range": null, "title": "Allow additional read protecting of efuses", "type": "bool"}, {"children": [], "depends_on": "SECURE_BOOT_INSECURE && !IDF_TARGET_ESP32", "help": "If not set (default), during startup in the app all unused digest slots will be revoked.\nTo revoke unused slot will be called esp_efuse_set_digest_revoke(num_digest) for each digest.\nRevoking unused digest slots makes ensures that no trusted keys can be added later by an attacker.\nIf set, it means that you have a plan to use unused digests slots later.", "id": "SECURE_BOOT_ALLOW_UNUSED_DIGEST_SLOTS", "name": "SECURE_BOOT_ALLOW_UNUSED_DIGEST_SLOTS", "range": null, "title": "Leave unused digest slots available (not revoke)", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "help": "If not set (default), the bootloader will permanently disable UART bootloader encryption access on\nfirst boot. If set, the UART bootloader will still be able to access hardware encryption.\n\nIt is recommended to only set this option in testing environments.", "id": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_ENC", "name": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_ENC", "range": null, "title": "Leave UART bootloader encryption enabled", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT && IDF_TARGET_ESP32", "help": "If not set (default), the bootloader will permanently disable UART bootloader decryption access on\nfirst boot. If set, the UART bootloader will still be able to access hardware decryption.\n\nOnly set this option in testing environments. Setting this option allows complete bypass of flash\nencryption.", "id": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_DEC", "name": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_DEC", "range": null, "title": "Leave UART bootloader decryption enabled", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "help": "If not set (default), the bootloader will permanently disable UART bootloader flash cache access on\nfirst boot. If set, the UART bootloader will still be able to access the flash cache.\n\nOnly set this option in testing environments.", "id": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_CACHE", "name": "SECURE_FLASH_UART_BOOTLOADER_ALLOW_CACHE", "range": null, "title": "Leave UART bootloader flash cache enabled", "type": "bool"}, {"children": [], "depends_on": "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT", "help": "If not set (default), and flash encryption is not yet enabled in eFuses, the 2nd stage bootloader\nwill enable flash encryption: generate the flash encryption key and program eFuses.\nIf this option is set, and flash encryption is not yet enabled, the bootloader will error out and\nreboot.\nIf flash encryption is enabled in eFuses, this option does not change the bootloader behavior.\n\nOnly use this option in testing environments, to avoid accidentally enabling flash encryption on\nthe wrong device. The device needs to have flash encryption already enabled using espefuse.py.", "id": "SECURE_FLASH_REQUIRE_ALREADY_ENABLED", "name": "SECURE_FLASH_REQUIRE_ALREADY_ENABLED", "range": null, "title": "Require flash encryption to be already enabled", "type": "bool"}], "depends_on": null, "id": "security-features-potentially-insecure-options", "title": "Potentially insecure options", "type": "menu"}, {"children": [], "depends_on": "SECURE_FLASH_ENC_ENABLED", "help": "If set (default), in an app during startup code,\nthere is a check of the flash encryption eFuse bit is on\n(as the bootloader should already have set it).\nThe app requires this bit is on to continue work otherwise abort.\n\nIf not set, the app does not care if the flash encryption eFuse bit is set or not.", "id": "SECURE_FLASH_CHECK_ENC_EN_IN_APP", "name": "SECURE_FLASH_CHECK_ENC_EN_IN_APP", "range": null, "title": "Check Flash Encryption enabled on app startup", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SECURE_UART_ROM_DL_MODE>", "help": "If set, during startup the app will burn an eFuse bit to permanently disable the UART ROM\nDownload Mode. This prevents any future use of esptool.py, espefuse.py and similar tools.\n\nOnce disabled, if the SoC is booted with strapping pins set for ROM Download Mode\nthen an error is printed instead.\n\nIt is recommended to enable this option in any production application where Flash\nEncryption and/or Secure Boot is enabled and access to Download Mode is not required.\n\nIt is also possible to permanently disable Download Mode by calling\nesp_efuse_disable_rom_download_mode() at runtime.", "id": "SECURE_DISABLE_ROM_DL_MODE", "name": "SECURE_DISABLE_ROM_DL_MODE", "range": null, "title": "UART ROM download mode (Permanently disabled (recommended))", "type": "bool"}, {"children": [], "depends_on": "SECURE_TARGET_HAS_SECURE_ROM_DL_MODE && <choice SECURE_UART_ROM_DL_MODE>", "help": "If set, during startup the app will burn an eFuse bit to permanently switch the UART ROM\nDownload Mode into a separate Secure Download mode. This option can only work if\nDownload Mode is not already disabled by eFuse.\n\nSecure Download mode limits the use of Download Mode functions to simple flash read,\nwrite and erase operations, plus a command to return a summary of currently enabled\nsecurity features.\n\nSecure Download mode is not compatible with the esptool.py flasher stub feature,\nespefuse.py, read/writing memory or registers, encrypted download, or any other\nfeatures that interact with unsupported Download Mode commands.\n\nSecure Download mode should be enabled in any application where Flash Encryption\nand/or Secure Boot is enabled. Disabling this option does not immediately cancel\nthe benefits of the security features, but it increases the potential \"attack\nsurface\" for an attacker to try and bypass them with a successful physical attack.\n\nIt is also possible to enable secure download mode at runtime by calling\nesp_efuse_enable_rom_secure_download_mode()\n\nNote: Secure Download mode is not available for ESP32 (includes revisions till ECO3).", "id": "SECURE_ENABLE_SECURE_ROM_DL_MODE", "name": "SECURE_ENABLE_SECURE_ROM_DL_MODE", "range": null, "title": "UART ROM download mode (Permanently switch to Secure mode (recommended))", "type": "bool"}, {"children": [], "depends_on": "<choice SECURE_UART_ROM_DL_MODE>", "help": "This is a potentially insecure option.\nEnabling this option will allow the full UART download mode to stay enabled.\nThis option SHOULD NOT BE ENABLED for production use cases.", "id": "SECURE_INSECURE_ALLOW_DL_MODE", "name": "SECURE_INSECURE_ALLOW_DL_MODE", "range": null, "title": "UART ROM download mode (Enabled (not recommended))", "type": "bool"}], "depends_on": "(SECURE_BOOT_V2_ENABLED || SECURE_FLASH_ENC_ENABLED) && (!IDF_TARGET_ESP32 || ESP32_REV_MIN_3)", "help": null, "id": "security-features-uart-rom-download-mode", "name": "SECURE_UART_ROM_DL_MODE", "title": "UART ROM download mode", "type": "choice"}], "depends_on": null, "id": "security-features", "title": "Security features", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Always print ROM logs, this is the default behavior.", "id": "BOOT_ROM_LOG_ALWAYS_ON", "name": "BOOT_ROM_LOG_ALWAYS_ON", "range": null, "title": "Always Log", "type": "bool"}, {"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Don't print ROM logs.", "id": "BOOT_ROM_LOG_ALWAYS_OFF", "name": "BOOT_ROM_LOG_ALWAYS_OFF", "range": null, "title": "Permanently disable logging", "type": "bool"}, {"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Print ROM logs when GPIO level is high during start up.\nThe GPIO number is chip dependent,\ne.g. on ESP32-S2, the control GPIO is GPIO46.", "id": "BOOT_ROM_LOG_ON_GPIO_HIGH", "name": "BOOT_ROM_LOG_ON_GPIO_HIGH", "range": null, "title": "Log on GPIO High", "type": "bool"}, {"children": [], "depends_on": "<choice BOOT_ROM_LOG_SCHEME>", "help": "Print ROM logs when GPIO level is low during start up.\nThe GPIO number is chip dependent,\ne.g. on ESP32-S2, the control GPIO is GPIO46.", "id": "BOOT_ROM_LOG_ON_GPIO_LOW", "name": "BOOT_ROM_LOG_ON_GPIO_LOW", "range": null, "title": "Log on GPIO Low", "type": "bool"}], "depends_on": "!IDF_TARGET_ESP32", "help": "Controls the Boot ROM log behavior.\nThe rom log behavior can only be changed for once,\nspecific eFuse bit(s) will be burned at app boot stage.", "id": "boot-rom-behavior-permanently-change-boot-rom-output", "name": "BOOT_ROM_LOG_SCHEME", "title": "Permanently change Boot ROM output", "type": "choice"}], "depends_on": null, "id": "boot-rom-behavior", "title": "Boot ROM Behavior", "type": "menu"}, {"children": [{"children": [], "depends_on": "!IDF_CMAKE", "help": "The serial port that's connected to the ESP chip. This can be overridden by setting the ESPPORT\nenvironment variable.\n\nThis value is ignored when using the CMake-based build system or idf.py.", "id": "ESPTOOLPY_PORT", "name": "ESPTOOLPY_PORT", "range": null, "title": "Default serial port", "type": "string"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_BAUD>", "help": null, "id": "ESPTOOLPY_BAUD_115200B", "name": "ESPTOOLPY_BAUD_115200B", "range": null, "title": "115200 baud", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_BAUD>", "help": null, "id": "ESPTOOLPY_BAUD_230400B", "name": "ESPTOOLPY_BAUD_230400B", "range": null, "title": "230400 baud", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_BAUD>", "help": null, "id": "ESPTOOLPY_BAUD_921600B", "name": "ESPTOOLPY_BAUD_921600B", "range": null, "title": "921600 baud", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_BAUD>", "help": null, "id": "ESPTOOLPY_BAUD_2MB", "name": "ESPTOOLPY_BAUD_2MB", "range": null, "title": "2<PERSON><PERSON>ud", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_BAUD>", "help": null, "id": "ESPTOOLPY_BAUD_OTHER", "name": "ESPTOOLPY_BAUD_OTHER", "range": null, "title": "Other baud rate", "type": "bool"}], "depends_on": "!IDF_CMAKE", "help": "Default baud rate to use while communicating with the ESP chip. Can be overridden by\nsetting the ESPBAUD variable.\n\nThis value is ignored when using the CMake-based build system or idf.py.", "id": "serial-flasher-config-default-baud-rate", "name": "ESPTOOLPY_BAUD", "title": "Default baud rate", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_BAUD_OTHER_VAL", "name": "ESPTOOLPY_BAUD_OTHER_VAL", "range": null, "title": "Other baud rate value", "type": "int"}, {"children": [], "depends_on": "!IDF_CMAKE", "help": null, "id": "ESPTOOLPY_BAUD", "name": "ESPTOOLPY_BAUD", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "!IDF_CMAKE", "help": "The flasher tool can send data compressed using zlib, letting the ROM on the ESP chip\ndecompress it on the fly before flashing it. For most payloads, this should result in a\nspeed increase.", "id": "ESPTOOLPY_COMPRESSED", "name": "ESPTOOLPY_COMPRESSED", "range": null, "title": "Use compressed upload", "type": "bool"}, {"children": [], "depends_on": null, "help": "The flasher tool sends a precompiled download stub first by default. That stub allows things\nlike compressed downloads and more. Usually you should not need to disable that feature", "id": "ESPTOOLPY_NO_STUB", "name": "ESPTOOLPY_NO_STUB", "range": null, "title": "Disable download stub", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32S3", "help": null, "id": "ESPTOOLPY_OCT_FLASH", "name": "ESPTOOLPY_OCT_FLASH", "range": null, "title": "Enable Octal Flash", "type": "bool"}, {"children": [{"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_QIO", "name": "ESPTOOLPY_FLASHMODE_QIO", "range": null, "title": "QIO", "type": "bool"}, {"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_QOUT", "name": "ESPTOOLPY_FLASHMODE_QOUT", "range": null, "title": "QOUT", "type": "bool"}, {"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_DIO", "name": "ESPTOOLPY_FLASHMODE_DIO", "range": null, "title": "DIO", "type": "bool"}, {"children": [], "depends_on": "!ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_DOUT", "name": "ESPTOOLPY_FLASHMODE_DOUT", "range": null, "title": "DOUT", "type": "bool"}, {"children": [], "depends_on": "ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASHMODE>", "help": null, "id": "ESPTOOLPY_FLASHMODE_OPI", "name": "ESPTOOLPY_FLASHMODE_OPI", "range": null, "title": "OPI", "type": "bool"}], "depends_on": null, "help": "Mode the flash chip is flashed in, as well as the default mode for the\nbinary to run in.", "id": "serial-flasher-config-flash-spi-mode", "name": "ESPTOOLPY_FLASHMODE", "title": "Flash SPI mode", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_FLASH_SAMPLE_MODE>", "help": null, "id": "ESPTOOLPY_FLASH_SAMPLE_MODE_STR", "name": "ESPTOOLPY_FLASH_SAMPLE_MODE_STR", "range": null, "title": "STR Mode", "type": "bool"}, {"children": [], "depends_on": "ESPTOOLPY_OCT_FLASH && <choice ESPTOOLPY_FLASH_SAMPLE_MODE>", "help": null, "id": "ESPTOOLPY_FLASH_SAMPLE_MODE_DTR", "name": "ESPTOOLPY_FLASH_SAMPLE_MODE_DTR", "range": null, "title": "DTR Mode", "type": "bool"}], "depends_on": null, "help": null, "id": "serial-flasher-config-flash-sampling-mode", "name": "ESPTOOLPY_FLASH_SAMPLE_MODE", "title": "Flash Sampling Mode", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_FLASHMODE", "name": "ESPTOOLPY_FLASHMODE", "range": null, "title": null, "type": "string"}, {"children": [{"children": [], "depends_on": "IDF_TARGET_ESP32S3 && ESPTOOLPY_FLASH_SAMPLE_MODE_STR && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_120M", "name": "ESPTOOLPY_FLASHFREQ_120M", "range": null, "title": "120 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_80M", "name": "ESPTOOLPY_FLASHFREQ_80M", "range": null, "title": "80 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_40M", "name": "ESPTOOLPY_FLASHFREQ_40M", "range": null, "title": "40 MHz", "type": "bool"}, {"children": [], "depends_on": "(IDF_TARGET_ESP32 || IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32C3) && <choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_26M", "name": "ESPTOOLPY_FLASHFREQ_26M", "range": null, "title": "26 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHFREQ>", "help": null, "id": "ESPTOOLPY_FLASHFREQ_20M", "name": "ESPTOOLPY_FLASHFREQ_20M", "range": null, "title": "20 MHz", "type": "bool"}], "depends_on": null, "help": "The SPI flash frequency to be used.", "id": "serial-flasher-config-flash-spi-speed", "name": "ESPTOOLPY_FLASHFREQ", "title": "Flash SPI speed", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_FLASHFREQ", "name": "ESPTOOLPY_FLASHFREQ", "range": null, "title": null, "type": "string"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_1MB", "name": "ESPTOOLPY_FLASHSIZE_1MB", "range": null, "title": "1 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_2MB", "name": "ESPTOOLPY_FLASHSIZE_2MB", "range": null, "title": "2 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_4MB", "name": "ESPTOOLPY_FLASHSIZE_4MB", "range": null, "title": "4 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_8MB", "name": "ESPTOOLPY_FLASHSIZE_8MB", "range": null, "title": "8 MB", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_FLASHSIZE>", "help": null, "id": "ESPTOOLPY_FLASHSIZE_16MB", "name": "ESPTOOLPY_FLASHSIZE_16MB", "range": null, "title": "16 MB", "type": "bool"}], "depends_on": null, "help": "SPI flash size, in megabytes", "id": "serial-flasher-config-flash-size", "name": "ESPTOOLPY_FLASHSIZE", "title": "Flash size", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_FLASHSIZE", "name": "ESPTOOLPY_FLASHSIZE", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": null, "help": "If this option is set, flashing the project will automatically detect\nthe flash size of the target chip and update the bootloader image\nbefore it is flashed.", "id": "ESPTOOLPY_FLASHSIZE_DETECT", "name": "ESPTOOLPY_FLASHSIZE_DETECT", "range": null, "title": "Detect flash size when flashing bootloader", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_BEFORE>", "help": null, "id": "ESPTOOLPY_BEFORE_RESET", "name": "ESPTOOLPY_BEFORE_RESET", "range": null, "title": "Reset to bootloader", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_BEFORE>", "help": null, "id": "ESPTOOLPY_BEFORE_NORESET", "name": "ESPTOOLPY_BEFORE_NORESET", "range": null, "title": "No reset", "type": "bool"}], "depends_on": null, "help": "Configure whether esptool.py should reset the ESP32 before flashing.\n\nAutomatic resetting depends on the RTS & DTR signals being\nwired from the serial port to the ESP32. Most USB development\nboards do this internally.", "id": "serial-flasher-config-before-flashing", "name": "ESPTOOLPY_BEFORE", "title": "Before flashing", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_BEFORE", "name": "ESPTOOLPY_BEFORE", "range": null, "title": null, "type": "string"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_AFTER>", "help": null, "id": "ESPTOOLPY_AFTER_RESET", "name": "ESPTOOLPY_AFTER_RESET", "range": null, "title": "Reset after flashing", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_AFTER>", "help": null, "id": "ESPTOOLPY_AFTER_NORESET", "name": "ESPTOOLPY_AFTER_NORESET", "range": null, "title": "Stay in bootloader", "type": "bool"}], "depends_on": null, "help": "Configure whether esptool.py should reset the ESP32 after flashing.\n\nAutomatic resetting depends on the RTS & DTR signals being\nwired from the serial port to the ESP32. Most USB development\nboards do this internally.", "id": "serial-flasher-config-after-flashing", "name": "ESPTOOLPY_AFTER", "title": "After flashing", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_AFTER", "name": "ESPTOOLPY_AFTER", "range": null, "title": null, "type": "string"}, {"children": [{"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_CONSOLE", "name": "ESPTOOLPY_MONITOR_BAUD_CONSOLE", "range": null, "title": "Same as UART console baud rate", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_9600B", "name": "ESPTOOLPY_MONITOR_BAUD_9600B", "range": null, "title": "9600 bps", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_57600B", "name": "ESPTOOLPY_MONITOR_BAUD_57600B", "range": null, "title": "57600 bps", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_115200B", "name": "ESPTOOLPY_MONITOR_BAUD_115200B", "range": null, "title": "115200 bps", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_230400B", "name": "ESPTOOLPY_MONITOR_BAUD_230400B", "range": null, "title": "230400 bps", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_921600B", "name": "ESPTOOLPY_MONITOR_BAUD_921600B", "range": null, "title": "921600 bps", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_2MB", "name": "ESPTOOLPY_MONITOR_BAUD_2MB", "range": null, "title": "2 Mbps", "type": "bool"}, {"children": [], "depends_on": "<choice ESPTOOLPY_MONITOR_BAUD>", "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_OTHER", "name": "ESPTOOLPY_MONITOR_BAUD_OTHER", "range": null, "title": "Custom baud rate", "type": "bool"}], "depends_on": null, "help": "Baud rate to use when running 'idf.py monitor' or 'make monitor'\nto view serial output from a running chip.\n\nIf \"Same as UART Console baud rate\" is chosen then the value will\nfollow the \"UART Console baud rate\" config item.\n\nCan override by setting the MONITORBAUD environment variable.", "id": "serial-flasher-config--idf-py-monitor-baud-rate", "name": "ESPTOOLPY_MONITOR_BAUD", "title": "'idf.py monitor' baud rate", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_MONITOR_BAUD_OTHER_VAL", "name": "ESPTOOLPY_MONITOR_BAUD_OTHER_VAL", "range": null, "title": "Custom baud rate value", "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "ESPTOOLPY_MONITOR_BAUD", "name": "ESPTOOLPY_MONITOR_BAUD", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "serial-flasher-config", "title": "Serial flasher config", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "This is the default partition table, designed to fit into a 2MB or\nlarger flash with a single 1MB app partition.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp.csv\n\nThis partition table is not suitable for an app that needs OTA\n(over the air update) capability.", "id": "PARTITION_TABLE_SINGLE_APP", "name": "PARTITION_TABLE_SINGLE_APP", "range": null, "title": "Single factory app, no OTA", "type": "bool"}, {"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the default partition table, that expands\nthe 1MB app partition size to 1.5MB to fit more code.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp_large.csv\n\nThis partition table is not suitable for an app that needs OTA\n(over the air update) capability.", "id": "PARTITION_TABLE_SINGLE_APP_LARGE", "name": "PARTITION_TABLE_SINGLE_APP_LARGE", "range": null, "title": "Single factory app (large), no OTA", "type": "bool"}, {"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "This is a basic OTA-enabled partition table with a factory app\npartition plus two OTA app partitions. All are 1MB, so this\npartition table requires 4MB or larger flash size.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_two_ota.csv", "id": "PARTITION_TABLE_TWO_OTA", "name": "PARTITION_TABLE_TWO_OTA", "range": null, "title": "Factory app, two OTA definitions", "type": "bool"}, {"children": [], "depends_on": "<choice PARTITION_TABLE_TYPE>", "help": "Specify the path to the partition table CSV to use for your project.\n\nConsult the Partition Table section in the ESP-IDF Programmers Guide\nfor more information.", "id": "PARTITION_TABLE_CUSTOM", "name": "PARTITION_TABLE_CUSTOM", "range": null, "title": "Custom partition table CSV", "type": "bool"}, {"children": [], "depends_on": "!ESP32_COREDUMP_ENABLE_TO_FLASH && NVS_ENCRYPTION && <choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the default \"Single factory app, no OTA\" partition table\nthat supports encrypted NVS when using flash encryption. See the Flash Encryption section\nin the ESP-IDF Programmers Guide for more information.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp_encr_nvs.csv", "id": "PARTITION_TABLE_SINGLE_APP_ENCRYPTED_NVS", "name": "PARTITION_TABLE_SINGLE_APP_ENCRYPTED_NVS", "range": null, "title": "Single factory app, no OTA, encrypted NVS", "type": "bool"}, {"children": [], "depends_on": "!ESP32_COREDUMP_ENABLE_TO_FLASH && NVS_ENCRYPTION && <choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the \"Single factory app (large), no OTA\" partition table\nthat supports encrypted NVS when using flash encryption. See the Flash Encryption section\nin the ESP-IDF Programmers Guide for more information.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_singleapp_large_encr_nvs.csv", "id": "PARTITION_TABLE_SINGLE_APP_LARGE_ENC_NVS", "name": "PARTITION_TABLE_SINGLE_APP_LARGE_ENC_NVS", "range": null, "title": "Single factory app (large), no OTA, encrypted NVS", "type": "bool"}, {"children": [], "depends_on": "!ESP_COREDUMP_ENABLE_TO_FLASH && NVS_ENCRYPTION && <choice PARTITION_TABLE_TYPE>", "help": "This is a variation of the \"Factory app, two OTA definitions\" partition table\nthat supports encrypted NVS when using flash encryption. See the Flash Encryption section\nin the ESP-IDF Programmers Guide for more information.\n\nThe corresponding CSV file in the IDF directory is\ncomponents/partition_table/partitions_two_ota_encr_nvs.csv", "id": "PARTITION_TABLE_TWO_OTA_ENCRYPTED_NVS", "name": "PARTITION_TABLE_TWO_OTA_ENCRYPTED_NVS", "range": null, "title": "Factory app, two OTA definitions, encrypted NVS", "type": "bool"}], "depends_on": null, "help": "The partition table to flash to the ESP32. The partition table\ndetermines where apps, data and other resources are expected to\nbe found.\n\nThe predefined partition table CSV descriptions can be found\nin the components/partition_table directory. These are mostly intended\nfor example and development use, it's expect that for production use you\nwill copy one of these CSV files and create a custom partition CSV for\nyour application.", "id": "partition-table-partition-table", "name": "PARTITION_TABLE_TYPE", "title": "Partition Table", "type": "choice"}, {"children": [], "depends_on": null, "help": "Name of the custom partition CSV filename. This path is evaluated\nrelative to the project root directory.", "id": "PARTITION_TABLE_CUSTOM_FILENAME", "name": "PARTITION_TABLE_CUSTOM_FILENAME", "range": null, "title": "Custom partition CSV file", "type": "string"}, {"children": [], "depends_on": null, "help": null, "id": "PARTITION_TABLE_FILENAME", "name": "PARTITION_TABLE_FILENAME", "range": null, "title": null, "type": "string"}, {"children": [], "depends_on": null, "help": "The address of partition table (by default 0x8000).\nAllows you to move the partition table, it gives more space for the bootloader.\nNote that the bootloader and app will both need to be compiled with the same PARTITION_TABLE_OFFSET value.\n\nThis number should be a multiple of 0x1000.\n\nNote that partition offsets in the partition table CSV file may need to be changed if this value is set to\na higher value. To have each partition offset adapt to the configured partition table offset, leave all\npartition offsets blank in the CSV file.", "id": "PARTITION_TABLE_OFFSET", "name": "PARTITION_TABLE_OFFSET", "range": null, "title": "Offset of partition table", "type": "hex"}, {"children": [], "depends_on": "!ESP32_COMPATIBLE_PRE_V3_1_BOOTLOADERS", "help": "Generate an MD5 checksum for the partition table for protecting the\nintegrity of the table. The generation should be turned off for legacy\nbootloaders which cannot recognize the MD5 checksum in the partition\ntable.", "id": "PARTITION_TABLE_MD5", "name": "PARTITION_TABLE_MD5", "range": null, "title": "Generate an MD5 checksum for the partition table", "type": "bool"}], "depends_on": null, "id": "partition-table", "title": "Partition Table", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_DEFAULT", "name": "COMPILER_OPTIMIZATION_DEFAULT", "range": null, "title": "Debug (-Og)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_SIZE", "name": "COMPILER_OPTIMIZATION_SIZE", "range": null, "title": "Optimize for size (-Os)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_PERF", "name": "COMPILER_OPTIMIZATION_PERF", "range": null, "title": "Optimize for performance (-O2)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION>", "help": null, "id": "COMPILER_OPTIMIZATION_NONE", "name": "COMPILER_OPTIMIZATION_NONE", "range": null, "title": "Debug without optimization (-O0)", "type": "bool"}], "depends_on": null, "help": "This option sets compiler optimization level (gcc -O argument) for the app.\n\n- The \"Default\" setting will add the -0g flag to CFLAGS.\n- The \"Size\" setting will add the -0s flag to CFLAGS.\n- The \"Performance\" setting will add the -O2 flag to CFLAGS.\n- The \"None\" setting will add the -O0 flag to CFLAGS.\n\nThe \"Size\" setting cause the compiled code to be smaller and faster, but\nmay lead to difficulties of correlating code addresses to source file\nlines when debugging.\n\nThe \"Performance\" setting causes the compiled code to be larger and faster,\nbut will be easier to correlated code addresses to source file lines.\n\n\"None\" with -O0 produces compiled code without optimization.\n\nNote that custom optimization levels may be unsupported.\n\nCompiler optimization for the IDF bootloader is set separately,\nsee the BOOTLOADER_COMPILER_OPTIMIZATION setting.", "id": "compiler-options-optimization-level", "name": "COMPILER_OPTIMIZATION", "title": "Optimization Level", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION_ASSERTION_LEVEL>", "help": "Enable assertions. Assertion content and line number will be printed on failure.", "id": "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE", "name": "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE", "range": null, "title": "Enabled", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION_ASSERTION_LEVEL>", "help": "Enable silent assertions. Failed assertions will abort(), user needs to\nuse the aborting address to find the line number with the failed assertion.", "id": "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT", "name": "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT", "range": null, "title": "Silent (saves code size)", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_OPTIMIZATION_ASSERTION_LEVEL>", "help": "If assertions are disabled, -DNDEBUG is added to CPPFLAGS.", "id": "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE", "name": "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE", "range": null, "title": "Disabled (sets -DNDEBUG)", "type": "bool"}], "depends_on": null, "help": "Assertions can be:\n\n- Enabled. Failure will print verbose assertion details. This is the default.\n\n- Set to \"silent\" to save code size (failed assertions will abort() but user\n  needs to use the aborting address to find the line number with the failed assertion.)\n\n- Disabled entirely (not recommended for most configurations.) -DNDEBUG is added\n  to CPPFLAGS in this case.", "id": "compiler-options-assertion-level", "name": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL", "title": "Assertion level", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL", "name": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": "If enabled, the error messages will be discarded in following check macros:\n- ESP_RETURN_ON_ERROR\n- ESP_EXIT_ON_ERROR\n- ESP_RETURN_ON_FALSE\n- ESP_EXIT_ON_FALSE", "id": "COMPILER_OPTIMIZATION_CHECKS_SILENT", "name": "COMPILER_OPTIMIZATION_CHECKS_SILENT", "range": null, "title": "Disable messages in ESP_RETURN_ON_* and ESP_EXIT_ON_* macros", "type": "bool"}, {"children": [], "depends_on": "IDF_CMAKE", "help": "When expanding the __FILE__ and __BASE_FILE__ macros, replace paths inside ESP-IDF\nwith paths relative to the placeholder string \"IDF\", and convert paths inside the\nproject directory to relative paths.\n\nThis allows building the project with assertions or other code that embeds file paths,\nwithout the binary containing the exact path to the IDF or project directories.\n\nThis option passes -fmacro-prefix-map options to the GCC command line. To replace additional\npaths in your binaries, modify the project CMakeLists.txt file to pass custom -fmacro-prefix-map or\n-ffile-prefix-map arguments.", "id": "COMPILER_HIDE_PATHS_MACROS", "is_menuconfig": true, "name": "COMPILER_HIDE_PATHS_MACROS", "range": null, "title": "Replace ESP-IDF and project paths in binaries", "type": "menu"}, {"children": [{"children": [], "depends_on": "COMPILER_CXX_EXCEPTIONS", "help": "Size (in bytes) of the emergency memory pool for C++ exceptions. This pool will be used to allocate\nmemory for thrown exceptions when there is not enough memory on the heap.", "id": "COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE", "name": "COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE", "range": null, "title": "Emergency Pool Size", "type": "int"}], "depends_on": null, "help": "Enabling this option compiles all IDF C++ files with exception support enabled.\n\nDisabling this option disables C++ exception support in all compiled files, and any libstdc++ code\nwhich throws an exception will abort instead.\n\nEnabling this option currently adds an additional ~500 bytes of heap overhead\nwhen an exception is thrown in user code for the first time.", "id": "COMPILER_CXX_EXCEPTIONS", "is_menuconfig": true, "name": "COMPILER_CXX_EXCEPTIONS", "range": null, "title": "Enable C++ exceptions", "type": "menu"}, {"children": [], "depends_on": null, "help": "Enabling this option compiles all C++ files with RTTI support enabled.\nThis increases binary size (typically by tens of kB) but allows using\ndynamic_cast conversion and typeid operator.", "id": "COMPILER_CXX_RTTI", "name": "COMPILER_CXX_RTTI", "range": null, "title": "Enable C++ run-time type info (RTTI)", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_NONE", "name": "COMPILER_STACK_CHECK_MODE_NONE", "range": null, "title": "None", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_NORM", "name": "COMPILER_STACK_CHECK_MODE_NORM", "range": null, "title": "Normal", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_STRONG", "name": "COMPILER_STACK_CHECK_MODE_STRONG", "range": null, "title": "Strong", "type": "bool"}, {"children": [], "depends_on": "<choice COMPILER_STACK_CHECK_MODE>", "help": null, "id": "COMPILER_STACK_CHECK_MODE_ALL", "name": "COMPILER_STACK_CHECK_MODE_ALL", "range": null, "title": "Overall", "type": "bool"}], "depends_on": null, "help": "Stack smashing protection mode. Emit extra code to check for buffer overflows, such as stack\nsmashing attacks. This is done by adding a guard variable to functions with vulnerable objects.\nThe guards are initialized when a function is entered and then checked when the function exits.\nIf a guard check fails, program is halted. Protection has the following modes:\n\n- In NORMAL mode (GCC flag: -fstack-protector) only functions that call alloca, and functions with\n  buffers larger than 8 bytes are protected.\n\n- STRONG mode (GCC flag: -fstack-protector-strong) is like NORMAL, but includes additional functions\n  to be protected -- those that have local array definitions, or have references to local frame\n  addresses.\n\n- In OVERALL mode (GCC flag: -fstack-protector-all) all functions are protected.\n\nModes have the following impact on code performance and coverage:\n\n- performance: NORMAL > STRONG > OVERALL\n\n- coverage: NORMAL < STRONG < OVERALL\n\nThe performance impact includes increasing the amount of stack memory required for each task.", "id": "compiler-options-stack-smashing-protection-mode", "name": "COMPILER_STACK_CHECK_MODE", "title": "Stack smashing protection mode", "type": "choice"}, {"children": [], "depends_on": null, "help": "Stack smashing protection.", "id": "COMPILER_STACK_CHECK", "name": "COMPILER_STACK_CHECK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "Adds -Wwrite-strings flag for the C/C++ compilers.\n\nFor C, this gives string constants the type ``const char[]`` so that\ncopying the address of one into a non-const ``char *`` pointer\nproduces a warning. This warning helps to find at compile time code\nthat tries to write into a string constant.\n\nFor C++, this warns about the deprecated conversion from string\nliterals to ``char *``.", "id": "COMPILER_WARN_WRITE_STRINGS", "name": "COMPILER_WARN_WRITE_STRINGS", "range": null, "title": "Enable -Wwrite-strings warning flag", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ARCH_RISCV", "help": "Adds -msave-restore to C/C++ compilation flags.\n\nWhen this flag is enabled, compiler will call library functions to\nsave/restore registers in function prologues/epilogues. This results\nin lower overall code size, at the expense of slightly reduced performance.\n\nThis option can be enabled for RISC-V targets only.", "id": "COMPILER_SAVE_RESTORE_LIBCALLS", "name": "COMPILER_SAVE_RESTORE_LIBCALLS", "range": null, "title": "Enable -msave-restore flag to reduce code size", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this option if using GCC 6 or newer, and wanting to disable warnings which don't appear with\nGCC 5.", "id": "COMPILER_DISABLE_GCC8_WARNINGS", "name": "COMPILER_DISABLE_GCC8_WARNINGS", "range": null, "title": "Disable new warnings introduced in GCC 6 - 8", "type": "bool"}, {"children": [], "depends_on": null, "help": "If enabled, RTL files will be produced during compilation. These files\ncan be used by other tools, for example to calculate call graphs.", "id": "COMPILER_DUMP_RTL_FILES", "name": "COMPILER_DUMP_RTL_FILES", "range": null, "title": "Dump RTL files during compilation", "type": "bool"}], "depends_on": null, "id": "compiler-options", "title": "Compiler options", "type": "menu"}, {"children": [{"children": [{"children": [{"children": [], "depends_on": "EFUSE_CUSTOM_TABLE", "help": "Name of the custom eFuse CSV filename. This path is evaluated\nrelative to the project root directory.", "id": "EFUSE_CUSTOM_TABLE_FILENAME", "name": "EFUSE_CUSTOM_TABLE_FILENAME", "range": null, "title": "Custom eFuse CSV file", "type": "string"}], "depends_on": null, "help": "Allows to generate a structure for eFuse from the CSV file.", "id": "EFUSE_CUSTOM_TABLE", "name": "EFUSE_CUSTOM_TABLE", "range": null, "title": "Use custom eFuse table", "type": "bool"}, {"children": [{"children": [], "depends_on": "EFUSE_VIRTUAL", "help": "In addition to the \"Simulate eFuse operations in RAM\" option, this option just adds\na feature to keep eFuses after reboots in flash memory. To use this mode the partition_table\nshould have the `efuse` partition. partition.csv: \"efuse_em, data, efuse,   ,   0x2000,\"\n\nDuring startup, the eFuses are copied from flash or,\nin case if flash is empty, from real eFuse to RAM and then update flash.\nThis mode is useful when need to keep changes after reboot\n(testing secure_boot and flash_encryption).", "id": "EFUSE_VIRTUAL_KEEP_IN_FLASH", "name": "EFUSE_VIRTUAL_KEEP_IN_FLASH", "range": null, "title": "Keep eFuses in flash", "type": "bool"}], "depends_on": null, "help": "If \"n\" - No virtual mode. All eFuse operations are real and use eFuse registers.\nIf \"y\" - The virtual mode is enabled and all eFuse operations (read and write) are redirected\nto RAM instead of eFuse registers, all permanent changes (via eFuse) are disabled.\nLog output will state changes that would be applied, but they will not be.\n\nDuring startup, the eFuses are copied into RAM. This mode is useful for fast tests.", "id": "EFUSE_VIRTUAL", "name": "EFUSE_VIRTUAL", "range": null, "title": "Simulate eFuse operations in RAM", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice EFUSE_CODE_SCHEME_SELECTOR>", "help": null, "id": "EFUSE_CODE_SCHEME_COMPAT_NONE", "name": "EFUSE_CODE_SCHEME_COMPAT_NONE", "range": null, "title": "None Only", "type": "bool"}, {"children": [], "depends_on": "<choice EFUSE_CODE_SCHEME_SELECTOR>", "help": null, "id": "EFUSE_CODE_SCHEME_COMPAT_3_4", "name": "EFUSE_CODE_SCHEME_COMPAT_3_4", "range": null, "title": "3/4 and None", "type": "bool"}, {"children": [], "depends_on": "<choice EFUSE_CODE_SCHEME_SELECTOR>", "help": null, "id": "EFUSE_CODE_SCHEME_COMPAT_REPEAT", "name": "EFUSE_CODE_SCHEME_COMPAT_REPEAT", "range": null, "title": "Repeat, 3/4 and None (common table does not support it)", "type": "bool"}], "depends_on": "IDF_TARGET_ESP32", "help": "Selector eFuse code scheme.", "id": "component-config-efuse-bit-manager-coding-scheme-compatibility", "name": "EFUSE_CODE_SCHEME_SELECTOR", "title": "Coding Scheme Compatibility", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "EFUSE_MAX_BLK_LEN", "name": "EFUSE_MAX_BLK_LEN", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "component-config-efuse-bit-manager", "title": "eFuse Bit Manager", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "IDF_ENV_FPGA && <choice ESP32C3_DEFAULT_CPU_FREQ_MHZ>", "help": null, "id": "ESP32C3_DEFAULT_CPU_FREQ_40", "name": "ESP32C3_DEFAULT_CPU_FREQ_40", "range": null, "title": "40 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_DEFAULT_CPU_FREQ_MHZ>", "help": null, "id": "ESP32C3_DEFAULT_CPU_FREQ_80", "name": "ESP32C3_DEFAULT_CPU_FREQ_80", "range": null, "title": "80 MHz", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_DEFAULT_CPU_FREQ_MHZ>", "help": null, "id": "ESP32C3_DEFAULT_CPU_FREQ_160", "name": "ESP32C3_DEFAULT_CPU_FREQ_160", "range": null, "title": "160 MHz", "type": "bool"}], "depends_on": null, "help": "CPU frequency to be set on application startup.", "id": "component-config-esp32c3-specific-cpu-frequency", "name": "ESP32C3_DEFAULT_CPU_FREQ_MHZ", "title": "CPU frequency", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP32C3_DEFAULT_CPU_FREQ_MHZ", "name": "ESP32C3_DEFAULT_CPU_FREQ_MHZ", "range": null, "title": null, "type": "int"}, {"children": [{"children": [], "depends_on": "<choice ESP32C3_REV_MIN>", "help": null, "id": "ESP32C3_REV_MIN_0", "name": "ESP32C3_REV_MIN_0", "range": null, "title": "Rev 0", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_REV_MIN>", "help": null, "id": "ESP32C3_REV_MIN_1", "name": "ESP32C3_REV_MIN_1", "range": null, "title": "Rev 1", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_REV_MIN>", "help": null, "id": "ESP32C3_REV_MIN_2", "name": "ESP32C3_REV_MIN_2", "range": null, "title": "Rev 2", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_REV_MIN>", "help": null, "id": "ESP32C3_REV_MIN_3", "name": "ESP32C3_REV_MIN_3", "range": null, "title": "Rev 3", "type": "bool"}], "depends_on": null, "help": "Minimum revision that ESP-IDF would support.\n\nOnly supporting higher chip revisions can reduce binary size.", "id": "component-config-esp32c3-specific-minimum-supported-esp32-c3-revision", "name": "ESP32C3_REV_MIN", "title": "Minimum Supported ESP32-C3 Revision", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP32C3_REV_MIN", "name": "ESP32C3_REV_MIN", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": "The FreeRTOS panic and unhandled exception handers can detect a JTAG OCD debugger and\ninstead of panicking, have the debugger stop on the offending instruction.", "id": "ESP32C3_DEBUG_OCDAWARE", "name": "ESP32C3_DEBUG_OCDAWARE", "range": null, "title": "Make exception and panic handlers JTAG/OCD aware", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "<choice ESP32C3_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP32C3_BROWNOUT_DET_LVL_SEL_7", "name": "ESP32C3_BROWNOUT_DET_LVL_SEL_7", "range": null, "title": "2.51V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP32C3_BROWNOUT_DET_LVL_SEL_6", "name": "ESP32C3_BROWNOUT_DET_LVL_SEL_6", "range": null, "title": "2.64V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP32C3_BROWNOUT_DET_LVL_SEL_5", "name": "ESP32C3_BROWNOUT_DET_LVL_SEL_5", "range": null, "title": "2.76V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP32C3_BROWNOUT_DET_LVL_SEL_4", "name": "ESP32C3_BROWNOUT_DET_LVL_SEL_4", "range": null, "title": "2.92V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP32C3_BROWNOUT_DET_LVL_SEL_3", "name": "ESP32C3_BROWNOUT_DET_LVL_SEL_3", "range": null, "title": "3.10V", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_BROWNOUT_DET_LVL_SEL>", "help": null, "id": "ESP32C3_BROWNOUT_DET_LVL_SEL_2", "name": "ESP32C3_BROWNOUT_DET_LVL_SEL_2", "range": null, "title": "3.27V", "type": "bool"}], "depends_on": "ESP32C3_BROWNOUT_DET", "help": "The brownout detector will reset the chip when the supply voltage is approximately\nbelow this level. Note that there may be some variation of brownout voltage level\nbetween each chip.\n\n#The voltage levels here are estimates, more work needs to be done to figure out the exact voltages\n#of the brownout threshold levels.", "id": "component-config-esp32c3-specific-hardware-brownout-detect-reset-brownout-voltage-level", "name": "ESP32C3_BROWNOUT_DET_LVL_SEL", "title": "Brownout voltage level", "type": "choice"}], "depends_on": "!IDF_ENV_FPGA", "help": "The ESP32-C3 has a built-in brownout detector which can detect if the voltage is lower than\na specific value. If this happens, it will reset the chip in order to prevent unintended\nbehaviour.", "id": "ESP32C3_BROWNOUT_DET", "name": "ESP32C3_BROWNOUT_DET", "range": null, "title": "Hardware brownout detect & reset", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP32C3_BROWNOUT_DET_LVL", "name": "ESP32C3_BROWNOUT_DET_LVL", "range": null, "title": null, "type": "int"}, {"children": [{"children": [], "depends_on": "<choice ESP32C3_TIME_SYSCALL>", "help": null, "id": "ESP32C3_TIME_SYSCALL_USE_RTC_SYSTIMER", "name": "ESP32C3_TIME_SYSCALL_USE_RTC_SYSTIMER", "range": null, "title": "RTC and high-resolution timer", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_TIME_SYSCALL>", "help": null, "id": "ESP32C3_TIME_SYSCALL_USE_RTC", "name": "ESP32C3_TIME_SYSCALL_USE_RTC", "range": null, "title": "RTC", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_TIME_SYSCALL>", "help": null, "id": "ESP32C3_TIME_SYSCALL_USE_SYSTIMER", "name": "ESP32C3_TIME_SYSCALL_USE_SYSTIMER", "range": null, "title": "High-resolution timer", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_TIME_SYSCALL>", "help": null, "id": "ESP32C3_TIME_SYSCALL_USE_NONE", "name": "ESP32C3_TIME_SYSCALL_USE_NONE", "range": null, "title": "None", "type": "bool"}], "depends_on": null, "help": "This setting defines which hardware timers are used to\nimplement 'gettimeofday' and 'time' functions in C library.\n\n- If both high-resolution (systimer) and RTC timers are used, timekeeping will\n  continue in deep sleep. Time will be reported at 1 microsecond\n  resolution. This is the default, and the recommended option.\n- If only high-resolution timer (systimer) is used, gettimeofday will\n  provide time at microsecond resolution.\n  Time will not be preserved when going into deep sleep mode.\n- If only RTC timer is used, timekeeping will continue in\n  deep sleep, but time will be measured at 6.(6) microsecond\n  resolution. Also the gettimeofday function itself may take\n  longer to run.\n- If no timers are used, gettimeofday and time functions\n  return -1 and set errno to ENOSYS.\n- When RTC is used for timekeeping, two RTC_STORE registers are\n  used to keep time in deep sleep mode.", "id": "component-config-esp32c3-specific-timers-used-for-gettimeofday-function", "name": "ESP32C3_TIME_SYSCALL", "title": "Timers used for gettimeofday function", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice ESP32C3_RTC_CLK_SRC>", "help": null, "id": "ESP32C3_RTC_CLK_SRC_INT_RC", "name": "ESP32C3_RTC_CLK_SRC_INT_RC", "range": null, "title": "Internal 150kHz RC oscillator", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_RTC_CLK_SRC>", "help": null, "id": "ESP32C3_RTC_CLK_SRC_EXT_CRYS", "name": "ESP32C3_RTC_CLK_SRC_EXT_CRYS", "range": null, "title": "External 32kHz crystal", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_RTC_CLK_SRC>", "help": null, "id": "ESP32C3_RTC_CLK_SRC_EXT_OSC", "name": "ESP32C3_RTC_CLK_SRC_EXT_OSC", "range": null, "title": "External 32kHz oscillator at 32K_XP pin", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_RTC_CLK_SRC>", "help": null, "id": "ESP32C3_RTC_CLK_SRC_INT_8MD256", "name": "ESP32C3_RTC_CLK_SRC_INT_8MD256", "range": null, "title": "Internal 8MHz oscillator, divided by 256 (~32kHz)", "type": "bool"}], "depends_on": null, "help": "Choose which clock is used as RTC clock source.", "id": "component-config-esp32c3-specific-rtc-clock-source", "name": "ESP32C3_RTC_CLK_SRC", "title": "RTC clock source", "type": "choice"}, {"children": [], "depends_on": null, "help": "When the startup code initializes RTC_SLOW_CLK, it can perform\ncalibration by comparing the RTC_SLOW_CLK frequency with main XTAL\nfrequency. This option sets the number of RTC_SLOW_CLK cycles measured\nby the calibration routine. Higher numbers increase calibration\nprecision, which may be important for applications which spend a lot of\ntime in deep sleep. Lower numbers reduce startup time.\n\nWhen this option is set to 0, clock calibration will not be performed at\nstartup, and approximate clock frequencies will be assumed:\n\n- 150000 Hz if internal RC oscillator is used as clock source. For this use value 1024.\n- 32768 Hz if the 32k crystal oscillator is used. For this use value 3000 or more.\n  In case more value will help improve the definition of the launch of the crystal.\n  If the crystal could not start, it will be switched to internal RC.", "id": "ESP32C3_RTC_CLK_CAL_CYCLES", "name": "ESP32C3_RTC_CLK_CAL_CYCLES", "range": [0, 32766], "title": "Number of cycles for RTC_SLOW_CLK calibration", "type": "int"}, {"children": [], "depends_on": "!BT_ENABLED", "help": "If enabled, this disables the linking of binary libraries in the application build. Note\nthat after enabling this Wi-Fi/Bluetooth will not work.", "id": "ESP32C3_NO_BLOBS", "name": "ESP32C3_NO_BLOBS", "range": null, "title": "No Binary Blobs", "type": "bool"}], "depends_on": null, "id": "component-config-esp32c3-specific", "title": "ESP32C3-Specific", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "Functions esp_err_to_name() and esp_err_to_name_r() return string representations of error codes from a\npre-generated lookup table. This option can be used to turn off the use of the look-up table in order to\nsave memory but this comes at the price of sacrificing distinguishable (meaningful) output string\nrepresentations.", "id": "ESP_ERR_TO_NAME_LOOKUP", "name": "ESP_ERR_TO_NAME_LOOKUP", "range": null, "title": "Enable lookup of error code strings", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY", "name": "ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY", "range": null, "title": null, "type": "bool"}], "depends_on": null, "id": "component-config-common-esp-related", "title": "Common ESP-related", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_WIFI_STA", "name": "ESP_MAC_ADDR_UNIVERSE_WIFI_STA", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_WIFI_AP", "name": "ESP_MAC_ADDR_UNIVERSE_WIFI_AP", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_BT", "name": "ESP_MAC_ADDR_UNIVERSE_BT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAC_ADDR_UNIVERSE_ETH", "name": "ESP_MAC_ADDR_UNIVERSE_ETH", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice ESP32C3_UNIVERSAL_MAC_ADDRESSES>", "help": null, "id": "ESP32C3_UNIVERSAL_MAC_ADDRESSES_TWO", "name": "ESP32C3_UNIVERSAL_MAC_ADDRESSES_TWO", "range": null, "title": "Two", "type": "bool"}, {"children": [], "depends_on": "<choice ESP32C3_UNIVERSAL_MAC_ADDRESSES>", "help": null, "id": "ESP32C3_UNIVERSAL_MAC_ADDRESSES_FOUR", "name": "ESP32C3_UNIVERSAL_MAC_ADDRESSES_FOUR", "range": null, "title": "Four", "type": "bool"}], "depends_on": null, "help": "Configure the number of universally administered (by IEEE) MAC addresses.\n\nDuring initialization, MAC addresses for each network interface are generated or derived from a\nsingle base MAC address.\n\nIf the number of universal MAC addresses is four, all four interfaces (WiFi station, WiFi softap,\nBluetooth and Ethernet) receive a universally administered MAC address. These are generated\nsequentially by adding 0, 1, 2 and 3 (respectively) to the final octet of the base MAC address.\n\nIf the number of universal MAC addresses is two, only two interfaces (WiFi station and Bluetooth)\nreceive a universally administered MAC address. These are generated sequentially by adding 0\nand 1 (respectively) to the base MAC address. The remaining two interfaces (WiFi softap and Ethernet)\nreceive local MAC addresses. These are derived from the universal WiFi station and Bluetooth MAC\naddresses, respectively.\n\nWhen using the default (Espressif-assigned) base MAC address, either setting can be used. When using\na custom universal MAC address range, the correct setting will depend on the allocation of MAC\naddresses in this range (either 2 or 4 per device.)\n\nNote that ESP32-C3 has no integrated Ethernet MAC. Although it's possible to use the esp_read_mac()\nAPI to return a MAC for Ethernet, this can only be used with an external MAC peripheral.", "id": "component-config-hardware-settings-mac-config-number-of-universally-administered-by-ieee-mac-address", "name": "ESP32C3_UNIVERSAL_MAC_ADDRESSES", "title": "Number of universally administered (by IEEE) MAC address", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP32C3_UNIVERSAL_MAC_ADDRESSES", "name": "ESP32C3_UNIVERSAL_MAC_ADDRESSES", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "component-config-hardware-settings-mac-config", "title": "MAC Config", "type": "menu"}, {"children": [{"children": [], "depends_on": "!SPIRAM", "help": "If enabled, chip will try to power down flash as part of esp_light_sleep_start(), which costs\nmore time when chip wakes up. Can only be enabled if there is no SPIRAM configured.\nThis option will in fact consider VDD_SDIO auto power value (ESP_PD_OPTION_AUTO) as OFF. Also, it is\npossible to force a power domain to stay ON during light sleep by using esp_sleep_pd_config()\nfunction.", "id": "ESP_SLEEP_POWER_DOWN_FLASH", "name": "ESP_SLEEP_POWER_DOWN_FLASH", "range": null, "title": "Power down flash in light sleep when there is no SPIRAM", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_SLEEP_RTC_BUS_ISO_WORKAROUND", "name": "ESP_SLEEP_RTC_BUS_ISO_WORKAROUND", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "esp32c3 and esp32s3 will reset at wake-up if GPIO is received a small electrostatic\npulse during light sleep, with specific condition\n\n- GPIO needs to be configured as input-mode only\n- The pin receives a small electrostatic pulse, and reset occurs when the pulse\n  voltage is higher than 6 V\n\nFor GPIO set to input mode only, it is not a good practice to leave it open/floating,\nThe hardware design needs to controlled it with determined supply or ground voltage\nis necessary.\n\nThis option provides a software workaround for this issue. Configure to isolate all\nGPIO pins in sleep state.", "id": "ESP_SLEEP_GPIO_RESET_WORKAROUND", "name": "ESP_SLEEP_GPIO_RESET_WORKAROUND", "range": null, "title": "light sleep GPIO reset workaround", "type": "bool"}, {"children": [], "depends_on": "SPIRAM", "help": "When the CS pin of SPIRAM is not pulled up, the sleep current will\nincrease during light sleep. If the CS pin of SPIRAM has an external\npull-up, you do not need to select this option, otherwise, you\nshould enable this option.", "id": "ESP_SLEEP_PSRAM_LEAKAGE_WORKAROUND", "name": "ESP_SLEEP_PSRAM_LEAKAGE_WORKAROUND", "range": null, "title": "PSRAM leakage current workaround in light sleep", "type": "bool"}, {"children": [], "depends_on": null, "help": "When the CS pin of Flash is not pulled up, the sleep current will\nincrease during light sleep. If the CS pin of Flash has an external\npull-up, you do not need to select this option, otherwise, you\nshould enable this option.", "id": "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND", "name": "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND", "range": null, "title": "Flash leakage current workaround in light sleep", "type": "bool"}], "depends_on": null, "id": "component-config-hardware-settings-sleep-config", "title": "Sleep Config", "type": "menu"}, {"children": [{"children": [], "depends_on": "ESP_CONSOLE_USB_SERIAL_JTAG || ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG", "help": "When the chip goes sleep or software reset, the clock source would change to XTAL\nand switch off the BBPLL clock for saving power. However, this might make the\nUSB_SERIAL_JTAG down which depends on BBPLL as its unique clock source.\nTherefore, this is used for keeping bbpll clock always on when USB_SERIAL_JTAG PORT is using.\nIf you want to use USB_SERIAL_JTAG under sw_reset case or sleep-wakeup case, you shoule select\nthis option. But be aware that this might increase the power consumption.", "id": "RTC_CLOCK_BBPLL_POWER_ON_WITH_USB", "name": "RTC_CLOCK_BBPLL_POWER_ON_WITH_USB", "range": null, "title": "Keep BBPLL clock always work", "type": "bool"}], "depends_on": null, "id": "component-config-hardware-settings-rtc-clock-config", "title": "RTC Clock Config", "type": "menu"}], "depends_on": null, "id": "component-config-hardware-settings", "title": "Hardware Settings", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Outputs the relevant registers over the serial port and halt the\nprocessor. Needs a manual reset to restart.", "id": "ESP_SYSTEM_PANIC_PRINT_HALT", "name": "ESP_SYSTEM_PANIC_PRINT_HALT", "range": null, "title": "Print registers and halt", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Outputs the relevant registers over the serial port and immediately\nreset the processor.", "id": "ESP_SYSTEM_PANIC_PRINT_REBOOT", "name": "ESP_SYSTEM_PANIC_PRINT_REBOOT", "range": null, "title": "Print registers and reboot", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Just resets the processor without outputting anything", "id": "ESP_SYSTEM_PANIC_SILENT_REBOOT", "name": "ESP_SYSTEM_PANIC_SILENT_REBOOT", "range": null, "title": "Silent reboot", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Invoke gdbstub on the serial port, allowing for gdb to attach to it to do a postmortem\nof the crash.", "id": "ESP_SYSTEM_PANIC_GDBSTUB", "name": "ESP_SYSTEM_PANIC_GDBSTUB", "range": null, "title": "GDBStub on panic", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_SYSTEM_PANIC>", "help": "Invoke gdbstub on the serial port, allowing for gdb to attach to it and to do a debug on runtime.\nThis feature will switch system to single core mode.", "id": "ESP_SYSTEM_GDBSTUB_RUNTIME", "name": "ESP_SYSTEM_GDBSTUB_RUNTIME", "range": null, "title": "GDBStub at runtime", "type": "bool"}], "depends_on": null, "help": "If FreeRTOS detects unexpected behaviour or an unhandled exception, the panic handler is\ninvoked. Configure the panic handler's action here.", "id": "component-config-esp-system-settings-panic-handler-behaviour", "name": "ESP_SYSTEM_PANIC", "title": "Panic handler behaviour", "type": "choice"}, {"children": [], "depends_on": null, "help": "Only initialize and use the main core.", "id": "ESP_SYSTEM_SINGLE_CORE_MODE", "name": "ESP_SYSTEM_SINGLE_CORE_MODE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_SYSTEM_RTC_EXT_XTAL", "name": "ESP_SYSTEM_RTC_EXT_XTAL", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_SYSTEM_RTC_EXT_OSC", "name": "ESP_SYSTEM_RTC_EXT_OSC", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "ESP_SYSTEM_RTC_EXT_XTAL", "help": "To reduce the startup time of an external RTC crystal,\nwe bootstrap it with a 32kHz square wave for a fixed number of cycles.\nSetting 0 will disable bootstrapping (if disabled, the crystal may take\nlonger to start up or fail to oscillate under some conditions).\n\nIf this value is too high, a faulty crystal may initially start and then fail.\nIf this value is too low, an otherwise good crystal may not start.\n\nTo accurately determine if the crystal has started,\nset a larger \"Number of cycles for RTC_SLOW_CLK calibration\" (about 3000).", "id": "ESP_SYSTEM_RTC_EXT_XTAL_BOOTSTRAP_CYCLES", "name": "ESP_SYSTEM_RTC_EXT_XTAL_BOOTSTRAP_CYCLES", "range": null, "title": "Bootstrap cycles for external 32kHz crystal", "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK", "name": "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK", "help": "This config option allows to add RTC fast memory region to system heap with capability\nsimilar to that of DRAM region but without DMA. This memory will be consumed first per\nheap initialization order by early startup services and scheduler related code. Speed\nwise RTC fast memory operates on APB clock and hence does not have much performance impact.", "id": "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP", "name": "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP", "range": null, "title": "Enable RTC fast memory for dynamic allocations", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ARCH_RISCV", "help": "Generate DWARF information for each function of the project. These information will parsed and used to\nperform backtracing when panics occur. Activating this option will activate asynchronous frame unwinding\nand generation of both .eh_frame and .eh_frame_hdr sections, resulting in a bigger binary size (20% to\n100% larger). The main purpose of this option is to be able to have a backtrace parsed and printed by\nthe program itself, regardless of the serial monitor used.\nThis option shall NOT be used for production.", "id": "ESP_SYSTEM_USE_EH_FRAME", "name": "ESP_SYSTEM_USE_EH_FRAME", "range": null, "title": "Generate and use eh_frame for backtracing", "type": "bool"}, {"children": [{"children": [], "depends_on": null, "help": null, "id": "ESP_SYSTEM_MEMPROT_DEPCHECK", "name": "ESP_SYSTEM_MEMPROT_DEPCHECK", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_SYSTEM_MEMPROT_FEATURE", "help": "Once locked, memory protection settings cannot be changed anymore.\nThe lock is reset only on the chip startup.", "id": "ESP_SYSTEM_MEMPROT_FEATURE_LOCK", "name": "ESP_SYSTEM_MEMPROT_FEATURE_LOCK", "range": null, "title": "Lock memory protection settings", "type": "bool"}], "depends_on": "ESP_SYSTEM_MEMPROT_DEPCHECK", "help": "If enabled, the permission control module watches all the memory access and fires the panic handler\nif a permission violation is detected. This feature automatically splits\nthe SRAM memory into data and instruction segments and sets Read/Execute permissions\nfor the instruction part (below given splitting address) and Read/Write permissions\nfor the data part (above the splitting address). The memory protection is effective\non all access through the IRAM0 and DRAM0 buses.", "id": "ESP_SYSTEM_MEMPROT_FEATURE", "name": "ESP_SYSTEM_MEMPROT_FEATURE", "range": null, "title": "Enable memory protection", "type": "bool"}, {"children": [], "depends_on": "ESP_SYSTEM_MEMPROT_DEPCHECK", "help": null, "id": "ESP_SYSTEM_MEMPROT_CPU_PREFETCH_PAD_SIZE", "name": "ESP_SYSTEM_MEMPROT_CPU_PREFETCH_PAD_SIZE", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "ESP_SYSTEM_MEMPROT_DEPCHECK", "help": null, "id": "ESP_SYSTEM_MEMPROT_MEM_ALIGN_SIZE", "name": "ESP_SYSTEM_MEMPROT_MEM_ALIGN_SIZE", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "component-config-esp-system-settings-memory-protection", "title": "Memory protection", "type": "menu"}, {"children": [], "depends_on": null, "help": "Config system event queue size in different application.", "id": "ESP_SYSTEM_EVENT_QUEUE_SIZE", "name": "ESP_SYSTEM_EVENT_QUEUE_SIZE", "range": null, "title": "System event queue size", "type": "int"}, {"children": [], "depends_on": null, "help": "Config system event task stack size in different application.", "id": "ESP_SYSTEM_EVENT_TASK_STACK_SIZE", "name": "ESP_SYSTEM_EVENT_TASK_STACK_SIZE", "range": null, "title": "Event loop task stack size", "type": "int"}, {"children": [], "depends_on": null, "help": "Configure the \"main task\" stack size. This is the stack of the task\nwhich calls app_main(). If app_main() returns then this task is deleted\nand its stack memory is freed.", "id": "ESP_MAIN_TASK_STACK_SIZE", "name": "ESP_MAIN_TASK_STACK_SIZE", "range": null, "title": "Main task stack size", "type": "int"}, {"children": [{"children": [], "depends_on": "<choice ESP_MAIN_TASK_AFFINITY>", "help": null, "id": "ESP_MAIN_TASK_AFFINITY_CPU0", "name": "ESP_MAIN_TASK_AFFINITY_CPU0", "range": null, "title": "CPU0", "type": "bool"}, {"children": [], "depends_on": "!FREERTOS_UNICORE && <choice ESP_MAIN_TASK_AFFINITY>", "help": null, "id": "ESP_MAIN_TASK_AFFINITY_CPU1", "name": "ESP_MAIN_TASK_AFFINITY_CPU1", "range": null, "title": "CPU1", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_MAIN_TASK_AFFINITY>", "help": null, "id": "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY", "name": "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY", "range": null, "title": "No affinity", "type": "bool"}], "depends_on": null, "help": "Configure the \"main task\" core affinity. This is the used core of the task\nwhich calls app_main(). If app_main() returns then this task is deleted.", "id": "component-config-esp-system-settings-main-task-core-affinity", "name": "ESP_MAIN_TASK_AFFINITY", "title": "Main task core affinity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_MAIN_TASK_AFFINITY", "name": "ESP_MAIN_TASK_AFFINITY", "range": null, "title": null, "type": "hex"}, {"children": [], "depends_on": null, "help": "Minimal value of size, in bytes, accepted to execute a expression\nwith shared stack.", "id": "ESP_MINIMAL_SHARED_STACK_SIZE", "name": "ESP_MINIMAL_SHARED_STACK_SIZE", "range": null, "title": "Minimal allowed size for shared stack", "type": "int"}, {"children": [{"children": [], "depends_on": "<choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_UART_DEFAULT", "name": "ESP_CONSOLE_UART_DEFAULT", "range": null, "title": "Default: UART0", "type": "bool"}, {"children": [], "depends_on": "(IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3) && !TINY_USB && <choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_USB_CDC", "name": "ESP_CONSOLE_USB_CDC", "range": null, "title": "USB CDC", "type": "bool"}, {"children": [], "depends_on": "(IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3) && <choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_USB_SERIAL_JTAG", "name": "ESP_CONSOLE_USB_SERIAL_JTAG", "range": null, "title": "USB Serial/JTAG Controller", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_UART_CUSTOM", "name": "ESP_CONSOLE_UART_CUSTOM", "range": null, "title": "Custom UART", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CONSOLE_UART>", "help": null, "id": "ESP_CONSOLE_NONE", "name": "ESP_CONSOLE_NONE", "range": null, "title": "None", "type": "bool"}], "depends_on": null, "help": "Select where to send console output (through stdout and stderr).\n\n- Default is to use UART0 on pre-defined GPIOs.\n- If \"Custom\" is selected, UART0 or UART1 can be chosen,\n  and any pins can be selected.\n- If \"None\" is selected, there will be no console output on any UART, except\n  for initial output from ROM bootloader. This ROM output can be suppressed by\n  GPIO strapping or EFUSE, refer to chip datasheet for details.\n- On chips with USB OTG peripheral, \"USB CDC\" option redirects output to the\n  CDC port. This option uses the CDC driver in the chip ROM.\n  This option is incompatible with TinyUSB stack.\n- On chips with an USB serial/JTAG debug controller, selecting the option\n  for that redirects output to the CDC/ACM (serial port emulation) component\n  of that device.", "id": "component-config-esp-system-settings-channel-for-console-output", "name": "ESP_CONSOLE_UART", "title": "Channel for console output", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice ESP_CONSOLE_SECONDARY>", "help": null, "id": "ESP_CONSOLE_SECONDARY_NONE", "name": "ESP_CONSOLE_SECONDARY_NONE", "range": null, "title": "No secondary console", "type": "bool"}, {"children": [], "depends_on": "!ESP_CONSOLE_USB_SERIAL_JTAG && <choice ESP_CONSOLE_SECONDARY>", "help": "This option supports output through USB_SERIAL_JTAG port when the UART0 port is not connected.\nThe output currently only supports non-blocking mode without using the console.\nIf you want to output in blocking mode with REPL or input through USB_SERIAL_JTAG port,\nplease change the primary config to ESP_CONSOLE_USB_SERIAL_JTAG above.", "id": "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG", "name": "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG", "range": null, "title": "USB_SERIAL_JTAG PORT", "type": "bool"}], "depends_on": "IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C3", "help": "This secondary option supports output through other specific port like USB_SERIAL_JTAG\nwhen UART0 port as a primary is selected but not connected. This secondary output currently only supports\nnon-blocking mode without using REPL. If you want to output in blocking mode with REPL or\ninput through this secondary port, please change the primary config to this port\nin `Channel for console output` menu.", "id": "component-config-esp-system-settings-channel-for-console-secondary-output", "name": "ESP_CONSOLE_SECONDARY", "title": "Channel for console secondary output", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_CONSOLE_UART", "name": "ESP_CONSOLE_UART", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_CONSOLE_MULTIPLE_UART", "name": "ESP_CONSOLE_MULTIPLE_UART", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice ESP_CONSOLE_UART_NUM>", "help": null, "id": "ESP_CONSOLE_UART_CUSTOM_NUM_0", "name": "ESP_CONSOLE_UART_CUSTOM_NUM_0", "range": null, "title": "UART0", "type": "bool"}, {"children": [], "depends_on": "<choice ESP_CONSOLE_UART_NUM>", "help": null, "id": "ESP_CONSOLE_UART_CUSTOM_NUM_1", "name": "ESP_CONSOLE_UART_CUSTOM_NUM_1", "range": null, "title": "UART1", "type": "bool"}], "depends_on": "ESP_CONSOLE_UART_CUSTOM && ESP_CONSOLE_MULTIPLE_UART", "help": "This UART peripheral is used for console output from the ESP-IDF Bootloader and the app.\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.\n\nDue to an ESP32 ROM bug, UART2 is not supported for console output\nvia esp_rom_printf.", "id": "component-config-esp-system-settings-uart-peripheral-to-use-for-console-output-0-1-", "name": "ESP_CONSOLE_UART_NUM", "title": "UART peripheral to use for console output (0-1)", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "ESP_CONSOLE_UART_NUM", "name": "ESP_CONSOLE_UART_NUM", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_UART_CUSTOM", "help": "This GPIO is used for console UART TX output in the ESP-IDF Bootloader and the app (including\nboot log output and default standard output and standard error of the app).\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.", "id": "ESP_CONSOLE_UART_TX_GPIO", "name": "ESP_CONSOLE_UART_TX_GPIO", "range": null, "title": "UART TX on GPIO#", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_UART_CUSTOM", "help": "This GPIO is used for UART RX input in the ESP-IDF Bootloader and the app (including\ndefault default standard input of the app).\n\nNote: The default ESP-IDF Bootloader configures this pin but doesn't read anything from the UART.\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.", "id": "ESP_CONSOLE_UART_RX_GPIO", "name": "ESP_CONSOLE_UART_RX_GPIO", "range": null, "title": "UART RX on GPIO#", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_UART", "help": "This baud rate is used by both the ESP-IDF Bootloader and the app (including\nboot log output and default standard input/output/error of the app).\n\nThe app's maximum baud rate depends on the UART clock source. If Power Management is disabled,\nthe UART clock source is the APB clock and all baud rates in the available range will be sufficiently\naccurate. If Power Management is enabled, REF_TICK clock source is used so the baud rate is divided\nfrom 1MHz. Baud rates above 1Mbps are not possible and values between 500Kbps and 1Mbps may not be\naccurate.\n\nIf the configuration is different in the Bootloader binary compared to the app binary, UART\nis reconfigured after the bootloader exits and the app starts.", "id": "ESP_CONSOLE_UART_BAUDRATE", "name": "ESP_CONSOLE_UART_BAUDRATE", "range": [1200, 4000000], "title": "UART console baud rate", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_USB_CDC", "help": "Set the size of USB CDC RX buffer. Increase the buffer size if your application\nis often receiving data over USB CDC.", "id": "ESP_CONSOLE_USB_CDC_RX_BUF_SIZE", "name": "ESP_CONSOLE_USB_CDC_RX_BUF_SIZE", "range": null, "title": "Size of USB CDC RX buffer", "type": "int"}, {"children": [], "depends_on": "ESP_CONSOLE_USB_CDC", "help": "If enabled, esp_rom_printf and ESP_EARLY_LOG output will also be sent over USB CDC.\nDisabling this option saves about 1kB or RAM.", "id": "ESP_CONSOLE_USB_CDC_SUPPORT_ETS_PRINTF", "name": "ESP_CONSOLE_USB_CDC_SUPPORT_ETS_PRINTF", "range": null, "title": "Enable esp_rom_printf / ESP_EARLY_LOG via USB CDC", "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_INT_WDT", "help": "The timeout of the watchdog, in miliseconds. Make this higher than the FreeRTOS tick rate.", "id": "ESP_INT_WDT_TIMEOUT_MS", "name": "ESP_INT_WDT_TIMEOUT_MS", "range": [10, 10000], "title": "Interrupt watchdog timeout (ms)", "type": "int"}, {"children": [], "depends_on": "ESP_INT_WDT && !FREERTOS_UNICORE", "help": "Also detect if interrupts on CPU 1 are disabled for too long.", "id": "ESP_INT_WDT_CHECK_CPU1", "name": "ESP_INT_WDT_CHECK_CPU1", "range": null, "title": "Also watch CPU1 tick interrupt", "type": "bool"}], "depends_on": null, "help": "This watchdog timer can detect if the FreeRTOS tick interrupt has not been called for a certain time,\neither because a task turned off interrupts and did not turn them on for a long time, or because an\ninterrupt handler did not return. It will try to invoke the panic handler first and failing that\nreset the SoC.", "id": "ESP_INT_WDT", "name": "ESP_INT_WDT", "range": null, "title": "Interrupt watchdog", "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_TASK_WDT", "help": "If this option is enabled, the Task Watchdog Timer will be configured to\ntrigger the panic handler when it times out. This can also be configured\nat run time (see Task Watchdog Timer API Reference)", "id": "ESP_TASK_WDT_PANIC", "name": "ESP_TASK_WDT_PANIC", "range": null, "title": "Invoke panic handler on Task Watchdog timeout", "type": "bool"}, {"children": [], "depends_on": "ESP_TASK_WDT", "help": "Timeout period configuration for the Task Watchdog Timer in seconds.\nThis is also configurable at run time (see Task Watchdog Timer API Reference)", "id": "ESP_TASK_WDT_TIMEOUT_S", "name": "ESP_TASK_WDT_TIMEOUT_S", "range": [1, 60], "title": "Task Watchdog timeout period (seconds)", "type": "int"}, {"children": [], "depends_on": "ESP_TASK_WDT", "help": "If this option is enabled, the Task Watchdog Timer will watch the CPU0\nIdle Task. Having the Task Watchdog watch the Idle Task allows for detection\nof CPU starvation as the Idle Task not being called is usually a symptom of\nCPU starvation. Starvation of the Idle Task is detrimental as FreeRTOS household\ntasks depend on the Idle Task getting some runtime every now and then.", "id": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0", "name": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0", "range": null, "title": "Watch CPU0 Idle Task", "type": "bool"}, {"children": [], "depends_on": "ESP_TASK_WDT && !FREERTOS_UNICORE", "help": "If this option is enabled, the Task Wtachdog Timer will wach the CPU1\nIdle Task.", "id": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1", "name": "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1", "range": null, "title": "Watch CPU1 Idle Task", "type": "bool"}], "depends_on": null, "help": "The Task Watchdog Timer can be used to make sure individual tasks are still\nrunning. Enabling this option will cause the Task Watchdog Timer to be\ninitialized automatically at startup. The Task Watchdog timer can be\ninitialized after startup as well (see Task Watchdog Timer API Reference)", "id": "ESP_TASK_WDT", "name": "ESP_TASK_WDT", "range": null, "title": "Initialize Task Watchdog Timer on startup", "type": "bool"}, {"children": [{"children": [], "depends_on": "ESP_XT_WDT", "help": "Timeout period configuration for the XTAL32K watchdog timer based on RTC_CLK.", "id": "ESP_XT_WDT_TIMEOUT", "name": "ESP_XT_WDT_TIMEOUT", "range": null, "title": "XTAL32K watchdog timeout period", "type": "int"}, {"children": [], "depends_on": "ESP_XT_WDT", "help": "Enable this to automatically switch to BACKUP32K_CLK as the source of RTC_SLOW_CLK when\nthe watchdog timer expires.", "id": "ESP_XT_WDT_BACKUP_CLK_ENABLE", "name": "ESP_XT_WDT_BACKUP_CLK_ENABLE", "range": null, "title": "Automatically switch to BACKUP32K_CLK when timer expires", "type": "bool"}], "depends_on": "!IDF_TARGET_ESP32 && (ESP_SYSTEM_RTC_EXT_OSC || ESP_SYSTEM_RTC_EXT_XTAL)", "help": "This watchdog timer can detect oscillation failure of the XTAL32K_CLK. When such a failure\nis detected the hardware can be set up to automatically switch to BACKUP32K_CLK and generate\nan interrupt.", "id": "ESP_XT_WDT", "name": "ESP_XT_WDT", "range": null, "title": "Initialize XTAL32K watchdog timer on startup", "type": "bool"}, {"children": [], "depends_on": null, "help": "If this option is disabled (default), the panic handler code is placed in flash not IRAM.\nThis means that if ESP-IDF crashes while flash cache is disabled, the panic handler will\nautomatically re-enable flash cache before running GDB Stub or Core Dump. This adds some minor\nrisk, if the flash cache status is also corrupted during the crash.\n\nIf this option is enabled, the panic handler code (including required UART functions) is placed\nin IRAM. This may be necessary to debug some complex issues with crashes while flash cache is\ndisabled (for example, when writing to SPI flash) or when flash cache is corrupted when an exception\nis triggered.", "id": "ESP_PANIC_HANDLER_IRAM", "name": "ESP_PANIC_HANDLER_IRAM", "range": null, "title": "Place panic handler code in IRAM", "type": "bool"}, {"children": [], "depends_on": "!ESP32_TRAX && !ESP32S2_TRAX && !ESP32S3_TRAX", "help": "Debug stubs are used by OpenOCD to execute pre-compiled onboard code\nwhich does some useful debugging stuff, e.g. GCOV data dump.", "id": "ESP_DEBUG_STUBS_ENABLE", "name": "ESP_DEBUG_STUBS_ENABLE", "range": null, "title": "OpenOCD debug stubs", "type": "bool"}, {"children": [{"children": [], "depends_on": "IDF_TARGET_ESP32 && <choice ESP_SYSTEM_CHECK_INT_LEVEL>", "help": "Using level 5 interrupt for Interrupt Watchdog and other system checks.", "id": "ESP_SYSTEM_CHECK_INT_LEVEL_5", "name": "ESP_SYSTEM_CHECK_INT_LEVEL_5", "range": null, "title": "Level 5 interrupt", "type": "bool"}, {"children": [], "depends_on": "!BTDM_CTRL_HLI && <choice ESP_SYSTEM_CHECK_INT_LEVEL>", "help": "Using level 4 interrupt for Interrupt Watchdog and other system checks.", "id": "ESP_SYSTEM_CHECK_INT_LEVEL_4", "name": "ESP_SYSTEM_CHECK_INT_LEVEL_4", "range": null, "title": "Level 4 interrupt", "type": "bool"}], "depends_on": null, "help": "Interrupt level to use for Interrupt Watchdog and other system checks.", "id": "component-config-esp-system-settings-interrupt-level-to-use-for-interrupt-watchdog-and-other-system-checks", "name": "ESP_SYSTEM_CHECK_INT_LEVEL", "title": "Interrupt level to use for Interrupt Watchdog and other system checks", "type": "choice"}], "depends_on": null, "id": "component-config-esp-system-settings", "title": "ESP System Settings", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "This version of FreeRTOS normally takes control of all cores of\nthe CPU. Select this if you only want to start it on the first core.\nThis is needed when e.g. another process needs complete control\nover the second core.\n\n# This invisible config value sets the value of tskNO_AFFINITY in task.h.\n# Intended to be used as a constant from other Kconfig files.\n# Value is (32-bit) INT_MAX.", "id": "FREERTOS_UNICORE", "name": "FREERTOS_UNICORE", "range": null, "title": "Run FreeRTOS only on first core", "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_NO_AFFINITY", "name": "FREERTOS_NO_AFFINITY", "range": null, "title": null, "type": "hex"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_TICK_SUPPORT_CORETIMER", "name": "FREERTOS_TICK_SUPPORT_CORETIMER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_TICK_SUPPORT_SYSTIMER", "name": "FREERTOS_TICK_SUPPORT_SYSTIMER", "range": null, "title": null, "type": "bool"}, {"children": [{"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_CORETIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use timer 0", "id": "FREERTOS_CORETIMER_0", "name": "FREERTOS_CORETIMER_0", "range": null, "title": "Timer 0 (int 6, level 1)", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_CORETIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use timer 1", "id": "FREERTOS_CORETIMER_1", "name": "FREERTOS_CORETIMER_1", "range": null, "title": "Timer 1 (int 15, level 3)", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_SYSTIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use systimer with the 1 interrupt priority.", "id": "FREERTOS_CORETIMER_SYSTIMER_LVL1", "name": "FREERTOS_CORETIMER_SYSTIMER_LVL1", "range": null, "title": "SYSTIMER 0 (level 1)", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_TICK_SUPPORT_SYSTIMER && <choice FREERTOS_CORETIMER>", "help": "Select this to use systimer with the 3 interrupt priority.", "id": "FREERTOS_CORETIMER_SYSTIMER_LVL3", "name": "FREERTOS_CORETIMER_SYSTIMER_LVL3", "range": null, "title": "SYSTIMER 0 (level 3)", "type": "bool"}], "depends_on": null, "help": "FreeRTOS needs a timer with an associated interrupt to use as\nthe main tick source to increase counters, run timers and do\npre-emptive multitasking with. There are multiple timers available\nto do this, with different interrupt priorities. Check", "id": "component-config-freertos-xtensa-timer-to-use-as-the-freertos-tick-source", "name": "FREERTOS_CORETIMER", "title": "Xtensa timer to use as the FreeRTOS tick source", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_SYSTICK_USES_SYSTIMER", "name": "FREERTOS_SYSTICK_USES_SYSTIMER", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_SYSTICK_USES_CCOUNT", "name": "FREERTOS_SYSTICK_USES_CCOUNT", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "FREERTOS_UNICORE", "help": "On most platforms there are instructions can speedup the ready task\nsearching. Enabling this option the FreeRTOS with this instructions\nsupport will be built.", "id": "FREERTOS_OPTIMIZED_SCHEDULER", "name": "FREERTOS_OPTIMIZED_SCHEDULER", "range": null, "title": "Enable FreeRTOS pĺatform optimized scheduler", "type": "bool"}, {"children": [], "depends_on": null, "help": "Select the tick rate at which FreeRTOS does pre-emptive context switching.", "id": "FREERTOS_HZ", "name": "FREERTOS_HZ", "range": [1, 1000], "title": "Tick rate (Hz)", "type": "int"}, {"children": [], "depends_on": null, "help": "Some functions in FreeRTOS have not been thoroughly tested yet when moving to\nthe SMP implementation of FreeRTOS. When this option is enabled, these fuctions\nwill throw an assert().", "id": "FREERTOS_ASSERT_ON_UNTESTED_FUNCTION", "name": "FREERTOS_ASSERT_ON_UNTESTED_FUNCTION", "range": null, "title": "Halt when an SMP-untested function is called", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice FREERTOS_CHECK_STACKOVERFLOW>", "help": "Do not check for stack overflows (configCHECK_FOR_STACK_OVERFLOW=0)", "id": "FREERTOS_CHECK_STACKOVERFLOW_NONE", "name": "FREERTOS_CHECK_STACKOVERFLOW_NONE", "range": null, "title": "No checking", "type": "bool"}, {"children": [], "depends_on": "<choice FREERTOS_CHECK_STACKOVERFLOW>", "help": "Check for stack overflows on each context switch by checking if\nthe stack pointer is in a valid range. Quick but does not detect\nstack overflows that happened between context switches\n(configCHECK_FOR_STACK_OVERFLOW=1)", "id": "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL", "name": "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL", "range": null, "title": "Check by stack pointer value", "type": "bool"}, {"children": [], "depends_on": "<choice FREERTOS_CHECK_STACKOVERFLOW>", "help": "Places some magic bytes at the end of the stack area and on each\ncontext switch, check if these bytes are still intact. More thorough\nthan just checking the pointer, but also slightly slower.\n(configCHECK_FOR_STACK_OVERFLOW=2)", "id": "FREERTOS_CHECK_STACKOVERFLOW_CANARY", "name": "FREERTOS_CHECK_STACKOVERFLOW_CANARY", "range": null, "title": "Check using canary bytes", "type": "bool"}], "depends_on": null, "help": "FreeRTOS can check for stack overflows in threads and trigger an user function\ncalled vApplicationStackOverflowHook when this happens.", "id": "component-config-freertos-check-for-stack-overflow", "name": "FREERTOS_CHECK_STACKOVERFLOW", "title": "Check for stack overflow", "type": "choice"}, {"children": [], "depends_on": null, "help": "FreeRTOS can check if a stack has overflown its bounds by checking either the value of\nthe stack pointer or by checking the integrity of canary bytes. (See FREERTOS_CHECK_STACKOVERFLOW\nfor more information.) These checks only happen on a context switch, and the situation that caused\nthe stack overflow may already be long gone by then. This option will use the last debug memory\nwatchpoint to allow breaking into the debugger (or panic'ing) as soon as any\nof the last 32 bytes on the stack of a task are overwritten. The side effect is that using gdb, you\neffectively have one hardware watchpoint less because the last one is overwritten as soon as a task\nswitch happens.\n\nAnother consequence is that due to alignment requirements of the watchpoint, the usable stack size\ndecreases by up to 60 bytes. This is because the watchpoint region has to be aligned to its size and the\nsize for the stack watchpoint in IDF is 32 bytes.\n\nThis check only triggers if the stack overflow writes within 32 bytes near the end of the stack, rather\nthan overshooting further, so it is worth combining this approach with one of the other stack overflow\ncheck methods.\n\nWhen this watchpoint is hit, gdb will stop with a SIGTRAP message. When no JTAG OCD is attached, esp-idf\nwill panic on an unhandled debug exception.", "id": "FREERTOS_WATCHPOINT_END_OF_STACK", "name": "FREERTOS_WATCHPOINT_END_OF_STACK", "range": null, "title": "Set a debug watchpoint as a stack overflow check", "type": "bool"}, {"children": [], "depends_on": null, "help": "If this option is enabled, interrupt stack frame will be modified to\npoint to the code of the interrupted task as its return address.\nThis helps the debugger (or the panic handler) show a backtrace from\nthe interrupt to the task which was interrupted. This also works for\nnested interrupts: higer level interrupt stack can be traced back to the\nlower level interrupt.\nThis option adds 4 instructions to the interrupt dispatching code.", "id": "FREERTOS_INTERRUPT_BACKTRACE", "name": "FREERTOS_INTERRUPT_BACKTRACE", "range": null, "title": "Enable backtrace from interrupt to task context", "type": "bool"}, {"children": [], "depends_on": null, "help": "FreeRTOS has the ability to store per-thread pointers in the task\ncontrol block. This controls the number of pointers available.\n\nThis value must be at least 1. Index 0 is reserved for use by the pthreads API\nthread-local-storage. Other indexes can be used for any desired purpose.", "id": "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS", "name": "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS", "range": [1, 256], "title": "Number of thread local storage pointers", "type": "int"}, {"children": [{"children": [], "depends_on": "!COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE && <choice FREERTOS_ASSERT>", "help": "If a FreeRTOS configASSERT() fails, FreeRTOS will abort() and\nhalt execution. The panic handler can be configured to handle\nthe outcome of an abort() in different ways.\n\nIf assertions are disabled for the entire project, they are also\ndisabled in FreeRTOS and this option is unavailable.", "id": "FREERTOS_ASSERT_FAIL_ABORT", "name": "FREERTOS_ASSERT_FAIL_ABORT", "range": null, "title": "abort() on failed assertions", "type": "bool"}, {"children": [], "depends_on": "<choice FREERTOS_ASSERT>", "help": "If a FreeRTOS assertion fails, print it out and continue.", "id": "FREERTOS_ASSERT_FAIL_PRINT_CONTINUE", "name": "FREERTOS_ASSERT_FAIL_PRINT_CONTINUE", "range": null, "title": "Print and continue failed assertions", "type": "bool"}, {"children": [], "depends_on": "<choice FREERTOS_ASSERT>", "help": "FreeRTOS configASSERT() will not be compiled into the binary.", "id": "FREERTOS_ASSERT_DISABLE", "name": "FREERTOS_ASSERT_DISABLE", "range": null, "title": "Disable FreeRTOS assertions", "type": "bool"}], "depends_on": null, "help": "Failed FreeRTOS configASSERT() assertions can be configured to\nbehave in different ways.\n\nBy default these behave the same as the global project assert settings.", "id": "component-config-freertos-freertos-assertions", "name": "FREERTOS_ASSERT", "title": "FreeRTOS assertions", "type": "choice"}, {"children": [], "depends_on": null, "help": "The idle task has its own stack, sized in bytes. The default size is enough for most uses. Size can be\nreduced to 768 bytes if no (or simple) FreeRTOS idle hooks are used and pthread local storage or FreeRTOS\nlocal storage cleanup callbacks are not used.\n\nThe stack size may need to be increased above the default if the app installs idle or thread local storage\ncleanup hooks that use a lot of stack memory.", "id": "FREERTOS_IDLE_TASK_STACKSIZE", "name": "FREERTOS_IDLE_TASK_STACKSIZE", "range": [768, 32768], "title": "Idle Task stack size", "type": "int"}, {"children": [], "depends_on": null, "help": "The interrupt handlers have their own stack. The size of the stack can be defined here.\nEach processor has its own stack, so the total size occupied will be twice this.", "id": "FREERTOS_ISR_STACKSIZE", "name": "FREERTOS_ISR_STACKSIZE", "range": [1536, 32768], "title": "ISR stack size", "type": "int"}, {"children": [], "depends_on": null, "help": "FreeRTOS offers a number of hooks/callback functions that are called when a timer\ntick happens, the idle thread runs etc. esp-idf replaces these by runtime registerable\nhooks using the esp_register_freertos_xxx_hook system, but for legacy reasons the old\nhooks can also still be enabled. Please enable this only if you have code that for some\nreason can't be migrated to the esp_register_freertos_xxx_hook system.", "id": "FREERTOS_LEGACY_HOOKS", "name": "FREERTOS_LEGACY_HOOKS", "range": null, "title": "Use FreeRTOS legacy hooks", "type": "bool"}, {"children": [], "depends_on": null, "help": "Changes the maximum task name length. Each task allocated will\ninclude this many bytes for a task name. Using a shorter value\nsaves a small amount of RAM, a longer value allows more complex\nnames.\n\nFor most uses, the default of 16 is OK.", "id": "FREERTOS_MAX_TASK_NAME_LEN", "name": "FREERTOS_MAX_TASK_NAME_LEN", "range": [1, 256], "title": "Maximum task name length", "type": "int"}, {"children": [], "depends_on": null, "help": null, "id": "FREERTOS_SUPPORT_STATIC_ALLOCATION", "name": "FREERTOS_SUPPORT_STATIC_ALLOCATION", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this option to make FreeRTOS call the static task clean up hook when a task is deleted.\n\nBear in mind that if this option is enabled you will need to implement the following function::\n\n    void vPortCleanUpTCB ( void *pxTCB ) {\n        // place clean up code here\n    }", "id": "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP", "name": "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP", "range": null, "title": "Enable static task clean up hook", "type": "bool"}, {"children": [], "depends_on": null, "help": "The timer service task (primarily) makes use of existing FreeRTOS features, allowing timer\nfunctionality to be added to an application with minimal impact on the size of the application's\nexecutable binary.\n\nUse this constant to define the priority that the timer task will run at.", "id": "FREERTOS_TIMER_TASK_PRIORITY", "name": "FREERTOS_TIMER_TASK_PRIORITY", "range": [1, 25], "title": "FreeRTOS timer task priority", "type": "int"}, {"children": [], "depends_on": null, "help": "The timer service task (primarily) makes use of existing FreeRTOS features, allowing timer\nfunctionality to be added to an application with minimal impact on the size of the application's\nexecutable binary.\n\nUse this constant to define the size (in bytes) of the stack allocated for the timer task.", "id": "FREERTOS_TIMER_TASK_STACK_DEPTH", "name": "FREERTOS_TIMER_TASK_STACK_DEPTH", "range": [1536, 32768], "title": "FreeRTOS timer task stack size", "type": "int"}, {"children": [], "depends_on": null, "help": "FreeRTOS provides a set of timer related API functions. Many of these functions use a standard\nFreeRTOS queue to send commands to the timer service task. The queue used for this purpose is\ncalled the 'timer command queue'. The 'timer command queue' is private to the FreeRTOS timer\nimplementation, and cannot be accessed directly.\n\nFor most uses the default value of 10 is OK.", "id": "FREERTOS_TIMER_QUEUE_LENGTH", "name": "FREERTOS_TIMER_QUEUE_LENGTH", "range": [5, 20], "title": "FreeRTOS timer queue length", "type": "int"}, {"children": [], "depends_on": null, "help": "FreeRTOS uses the queue registry as a means for kernel aware debuggers to locate queues, semaphores,\nand mutexes. The registry allows for a textual name to be associated with a queue for easy identification\nwithin a debugging GUI. A value of 0 will disable queue registry functionality, and a value larger than 0\nwill specify the number of queues/semaphores/mutexes that the registry can hold.", "id": "FREERTOS_QUEUE_REGISTRY_SIZE", "name": "FREERTOS_QUEUE_REGISTRY_SIZE", "range": [0, 20], "title": "FreeRTOS queue registry size", "type": "int"}, {"children": [{"children": [{"children": [], "depends_on": "FREERTOS_USE_STATS_FORMATTING_FUNCTIONS", "help": "If enabled, this will include an extra column when vTaskL<PERSON> is called\nto display the CoreID the task is pinned to (0,1) or -1 if not pinned.", "id": "FREERTOS_VTASKLIST_INCLUDE_COREID", "name": "FREERTOS_VTASKLIST_INCLUDE_COREID", "range": null, "title": "Enable display of xCoreID in vTaskList", "type": "bool"}], "depends_on": "FREERTOS_USE_TRACE_FACILITY", "help": "If enabled, configUSE_STATS_FORMATTING_FUNCTIONS will be defined as 1 in\nFreeRTOS. This will allow the usage of stats formatting functions such\nas vTaskList().", "id": "FREERTOS_USE_STATS_FORMATTING_FUNCTIONS", "name": "FREERTOS_USE_STATS_FORMATTING_FUNCTIONS", "range": null, "title": "Enable FreeRTOS stats formatting functions", "type": "bool"}], "depends_on": null, "help": "If enabled, configUSE_TRACE_FACILITY will be defined as 1 in FreeRTOS.\nThis will allow the usage of trace facility functions such as\nuxTaskGetSystemState().", "id": "FREERTOS_USE_TRACE_FACILITY", "name": "FREERTOS_USE_TRACE_FACILITY", "range": null, "title": "Enable FreeRTOS trace facility", "type": "bool"}, {"children": [{"children": [{"children": [], "depends_on": "<choice FREERTOS_RUN_TIME_STATS_CLK>", "help": "ESP Timer will be used as the clock source for FreeRTOS run time stats.\nThe ESP Timer runs at a frequency of 1MHz regardless of Dynamic\nFrequency Scaling. Therefore the ESP Timer will overflow in\napproximately 4290 seconds.", "id": "FREERTOS_RUN_TIME_STATS_USING_ESP_TIMER", "name": "FREERTOS_RUN_TIME_STATS_USING_ESP_TIMER", "range": null, "title": "Use ESP TIMER for run time stats", "type": "bool"}, {"children": [], "depends_on": "FREERTOS_SYSTICK_USES_CCOUNT && <choice FREERTOS_RUN_TIME_STATS_CLK>", "help": "CPU Clock will be used as the clock source for the generation of run\ntime stats. The CPU Clock has a frequency dependent on\nESP32_DEFAULT_CPU_FREQ_MHZ and Dynamic Frequency Scaling (DFS).\nTherefore the CPU Clock frequency can fluctuate between 80 to 240MHz.\nRun time stats generated using the CPU Clock represents the number of\nCPU cycles each task is allocated and DOES NOT reflect the amount of\ntime each task runs for (as CPU clock frequency can change). If the CPU\nclock consistently runs at the maximum frequency of 240MHz, it will\noverflow in approximately 17 seconds.", "id": "FREERTOS_RUN_TIME_STATS_USING_CPU_CLK", "name": "FREERTOS_RUN_TIME_STATS_USING_CPU_CLK", "range": null, "title": "Use CPU Clock for run time stats", "type": "bool"}], "depends_on": "FREERTOS_GENERATE_RUN_TIME_STATS", "help": "Choose the clock source for FreeRTOS run time stats. Options are CPU0's\nCPU Clock or the ESP Timer. Both clock sources are 32 bits. The CPU\nClock can run at a higher frequency hence provide a finer resolution\nbut will overflow much quicker. Note that run time stats are only valid\nuntil the clock source overflows.", "id": "component-config-freertos-enable-freertos-to-collect-run-time-stats-choose-the-clock-source-for-run-time-stats", "name": "FREERTOS_RUN_TIME_STATS_CLK", "title": "Choose the clock source for run time stats", "type": "choice"}], "depends_on": null, "help": "If enabled, configGENERATE_RUN_TIME_STATS will be defined as 1 in\nFreeRTOS. This will allow FreeRTOS to collect information regarding the\nusage of processor time amongst FreeRTOS tasks. Run time stats are\ngenerated using either the ESP Timer or the CPU Clock as the clock\nsource (Note that run time stats are only valid until the clock source\noverflows). The function vTaskGetRunTimeStats() will also be available\nif FREERTOS_USE_STATS_FORMATTING_FUNCTIONS and\nFREERTOS_USE_TRACE_FACILITY are enabled. vTaskGetRunTimeStats() will\ndisplay the run time of each task as a % of the total run time of all\nCPUs (task run time / no of CPUs) / (total run time / 100 )", "id": "FREERTOS_GENERATE_RUN_TIME_STATS", "name": "FREERTOS_GENERATE_RUN_TIME_STATS", "range": null, "title": "Enable FreeRTOS to collect run time stats", "type": "bool"}, {"children": [{"children": [], "depends_on": "FREERTOS_USE_TICKLESS_IDLE", "help": "FreeRTOS will enter light sleep mode if no tasks need to run for this number\nof ticks.", "id": "FREERTOS_IDLE_TIME_BEFORE_SLEEP", "name": "FREERTOS_IDLE_TIME_BEFORE_SLEEP", "range": null, "title": "Minimum number of ticks to enter sleep mode for", "type": "int"}], "depends_on": "PM_ENABLE", "help": "If power management support is enabled, FreeRTOS will be able to put\nthe system into light sleep mode when no tasks need to run for a number\nof ticks. This number can be set using FREERTOS_IDLE_TIME_BEFORE_SLEEP option.\nThis feature is also known as \"automatic light sleep\".\n\nNote that timers created using esp_timer APIs may prevent the system from\nentering sleep mode, even when no tasks need to run.\nTo skip unnecessary wake-up initialize a timer with the \"skip_unhandled_events\" option as true.\n\nIf disabled, automatic light sleep support will be disabled.", "id": "FREERTOS_USE_TICKLESS_IDLE", "name": "FREERTOS_USE_TICKLESS_IDLE", "range": null, "title": "Tickless idle support", "type": "bool"}, {"children": [], "depends_on": "COMPILER_OPTIMIZATION_DEFAULT", "help": "If enabled, all FreeRTOS task functions will be enclosed in a wrapper function.\nIf a task function mistakenly returns (i.e. does not delete), the call flow will\nreturn to the wrapper function. The wrapper function will then log an error and\nabort the application. This option is also required for GDB backtraces and C++\nexceptions to work correctly inside top-level task functions.", "id": "FREERTOS_TASK_FUNCTION_WRAPPER", "name": "FREERTOS_TASK_FUNCTION_WRAPPER", "range": null, "title": "Enclose all task functions in a wrapper function", "type": "bool"}, {"children": [], "depends_on": null, "help": "If enabled, assert that when a mutex semaphore is given, the task giving the\nsemaphore is the task which is currently holding the mutex.", "id": "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER", "name": "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER", "range": null, "title": "Check that mutex semaphore is given by owner task", "type": "bool"}, {"children": [], "depends_on": null, "help": "If enabled, context of port*_CRITICAL calls (ISR or Non-ISR)\nwould be checked to be in compliance with Vanilla FreeRTOS.\ne.g Calling port*_CRITICAL from ISR context would cause assert failure", "id": "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE", "name": "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE", "range": null, "title": "Tests compliance with Vanilla FreeRTOS port*_CRITICAL calls", "type": "bool"}, {"children": [], "depends_on": null, "help": "When enabled the selected Non-ISR FreeRTOS functions will be placed into Flash memory instead of IRAM.\nThis saves up to 8KB of IRAM depending on which functions are used.", "id": "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH", "name": "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH", "range": null, "title": "Place FreeRTOS functions into Flash", "type": "bool"}, {"children": [], "depends_on": null, "help": "Hidden option, gets selected by CONFIG_ESPxx_DEBUG_OCDAWARE", "id": "FREERTOS_DEBUG_OCDAWARE", "name": "FREERTOS_DEBUG_OCDAWARE", "range": null, "title": null, "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32", "help": "When enabled, the usage of float type is allowed inside Level 1\nISRs.", "id": "FREERTOS_FPU_IN_ISR", "name": "FREERTOS_FPU_IN_ISR", "range": null, "title": "Allow use of float inside Level 1 ISR (EXPERIMENTAL)", "type": "bool"}, {"children": [{"children": [], "depends_on": "FREERTOS_ENABLE_TASK_SNAPSHOT && !ESP_PANIC_HANDLER_IRAM", "help": "When enabled, the functions related to snapshots, such as vTaskGetSnapshot or uxTaskGetSnapshotAll,\nwill be placed in flash. Note that if enabled, these functions cannot be called when cache is disabled.", "id": "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH", "name": "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH", "range": null, "title": "Place task snapshot functions into flash", "type": "bool"}], "depends_on": null, "help": "When enabled, the functions related to snapshots, such as vTaskGetSnapshot or uxTaskGetSnapshotAll,\nare compiled and linked.", "id": "FREERTOS_ENABLE_TASK_SNAPSHOT", "name": "FREERTOS_ENABLE_TASK_SNAPSHOT", "range": null, "title": "Enable task snapshot functions", "type": "bool"}], "depends_on": null, "id": "component-config-freertos", "title": "FreeRTOS", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_EQUALS_SYSTEM", "name": "HAL_ASSERTION_EQUALS_SYSTEM", "range": null, "title": "Same as system assertion level", "type": "bool"}, {"children": [], "depends_on": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL >= 0 && <choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_DISABLE", "name": "HAL_ASSERTION_DISABLE", "range": null, "title": "Disabled", "type": "bool"}, {"children": [], "depends_on": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL >= 1 && <choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_SILIENT", "name": "HAL_ASSERTION_SILIENT", "range": null, "title": "Silent", "type": "bool"}, {"children": [], "depends_on": "COMPILER_OPTIMIZATION_ASSERTION_LEVEL >= 2 && <choice HAL_DEFAULT_ASSERTION_LEVEL>", "help": null, "id": "HAL_ASSERTION_ENABLE", "name": "HAL_ASSERTION_ENABLE", "range": null, "title": "Enabled", "type": "bool"}], "depends_on": null, "help": "Set the assert behavior / level for HAL component.\nHAL component assert level can be set separately,\nbut the level can't exceed the system assertion level.\ne.g. If the system assertion is disabled, then the HAL\nassertion can't be enabled either. If the system assertion\nis enable, then the HAL assertion can still be disabled\nby this Kconfig option.", "id": "component-config-hardware-abstraction-layer-hal-and-low-level-ll--default-hal-assertion-level", "name": "HAL_DEFAULT_ASSERTION_LEVEL", "title": "Default HAL assertion level", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "HAL_DEFAULT_ASSERTION_LEVEL", "name": "HAL_DEFAULT_ASSERTION_LEVEL", "range": null, "title": null, "type": "int"}], "depends_on": null, "id": "component-config-hardware-abstraction-layer-hal-and-low-level-ll-", "title": "Hardware Abstraction Layer (HAL) and Low Level (LL)", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_NONE", "name": "LOG_DEFAULT_LEVEL_NONE", "range": null, "title": "No output", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_ERROR", "name": "LOG_DEFAULT_LEVEL_ERROR", "range": null, "title": "Error", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_WARN", "name": "LOG_DEFAULT_LEVEL_WARN", "range": null, "title": "Warning", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_INFO", "name": "LOG_DEFAULT_LEVEL_INFO", "range": null, "title": "Info", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_DEBUG", "name": "LOG_DEFAULT_LEVEL_DEBUG", "range": null, "title": "Debug", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_DEFAULT_LEVEL>", "help": null, "id": "LOG_DEFAULT_LEVEL_VERBOSE", "name": "LOG_DEFAULT_LEVEL_VERBOSE", "range": null, "title": "Verbose", "type": "bool"}], "depends_on": null, "help": "Specify how much output to see in logs by default.\nYou can set lower verbosity level at runtime using\nesp_log_level_set function.\n\nBy default, this setting limits which log statements\nare compiled into the program. For example, selecting\n\"Warning\" would mean that changing log level to \"Debug\"\nat runtime will not be possible. To allow increasing log\nlevel above the default at runtime, see the next option.", "id": "component-config-log-output-default-log-verbosity", "name": "LOG_DEFAULT_LEVEL", "title": "Default log verbosity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "LOG_DEFAULT_LEVEL", "name": "LOG_DEFAULT_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [{"children": [], "depends_on": "<choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_EQUALS_DEFAULT", "name": "LOG_MAXIMUM_EQUALS_DEFAULT", "range": null, "title": "Same as default", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 1 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_ERROR", "name": "LOG_MAXIMUM_LEVEL_ERROR", "range": null, "title": "Error", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 2 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_WARN", "name": "LOG_MAXIMUM_LEVEL_WARN", "range": null, "title": "Warning", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 3 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_INFO", "name": "LOG_MAXIMUM_LEVEL_INFO", "range": null, "title": "Info", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 4 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_DEBUG", "name": "LOG_MAXIMUM_LEVEL_DEBUG", "range": null, "title": "Debug", "type": "bool"}, {"children": [], "depends_on": "LOG_DEFAULT_LEVEL < 5 && <choice LOG_MAXIMUM_LEVEL>", "help": null, "id": "LOG_MAXIMUM_LEVEL_VERBOSE", "name": "LOG_MAXIMUM_LEVEL_VERBOSE", "range": null, "title": "Verbose", "type": "bool"}], "depends_on": null, "help": "This config option sets the highest log verbosity that it's possible to select\nat runtime by calling esp_log_level_set(). This level may be higher than\nthe default verbosity level which is set when the app starts up.\n\nThis can be used enable debugging output only at a critical point, for a particular\ntag, or to minimize startup time but then enable more logs once the firmware has\nloaded.\n\nNote that increasing the maximum available log level will increase the firmware\nbinary size.\n\nThis option only applies to logging from the app, the bootloader log level is\nfixed at compile time to the separate \"Bootloader log verbosity\" setting.", "id": "component-config-log-output-maximum-log-verbosity", "name": "LOG_MAXIMUM_LEVEL", "title": "Maximum log verbosity", "type": "choice"}, {"children": [], "depends_on": null, "help": null, "id": "LOG_MAXIMUM_LEVEL", "name": "LOG_MAXIMUM_LEVEL", "range": null, "title": null, "type": "int"}, {"children": [], "depends_on": null, "help": "Enable ANSI terminal color codes in bootloader output.\n\nIn order to view these, your terminal program must support ANSI color codes.", "id": "LOG_COLORS", "name": "LOG_COLORS", "range": null, "title": "Use ANSI terminal colors in log output", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice LOG_TIMESTAMP_SOURCE>", "help": null, "id": "LOG_TIMESTAMP_SOURCE_RTOS", "name": "LOG_TIMESTAMP_SOURCE_RTOS", "range": null, "title": "Milliseconds Since <PERSON><PERSON>", "type": "bool"}, {"children": [], "depends_on": "<choice LOG_TIMESTAMP_SOURCE>", "help": null, "id": "LOG_TIMESTAMP_SOURCE_SYSTEM", "name": "LOG_TIMESTAMP_SOURCE_SYSTEM", "range": null, "title": "System Time", "type": "bool"}], "depends_on": null, "help": "Choose what sort of timestamp is displayed in the log output:\n\n- Milliseconds since boot is calulated from the RTOS tick count multiplied\n  by the tick period. This time will reset after a software reboot.\n  e.g. (90000)\n\n- System time is taken from POSIX time functions which use the ESP32's\n  RTC and FRC1 timers to maintain an accurate time. The system time is\n  initialized to 0 on startup, it can be set with an SNTP sync, or with\n  POSIX time functions. This time will not reset after a software reboot.\n  e.g. (00:01:30.000)\n\n- NOTE: Currently this will not get used in logging from binary blobs\n  (i.e WiFi & Bluetooth libraries), these will always print\n  milliseconds since boot.", "id": "component-config-log-output-log-timestamps", "name": "LOG_TIMESTAMP_SOURCE", "title": "Log Timestamps", "type": "choice"}], "depends_on": null, "id": "component-config-log-output", "title": "Log output", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "<choice NEWLIB_STDOUT_LINE_ENDING>", "help": null, "id": "NEWLIB_STDOUT_LINE_ENDING_CRLF", "name": "NEWLIB_STDOUT_LINE_ENDING_CRLF", "range": null, "title": "CRLF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDOUT_LINE_ENDING>", "help": null, "id": "NEWLIB_STDOUT_LINE_ENDING_LF", "name": "NEWLIB_STDOUT_LINE_ENDING_LF", "range": null, "title": "LF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDOUT_LINE_ENDING>", "help": null, "id": "NEWLIB_STDOUT_LINE_ENDING_CR", "name": "NEWLIB_STDOUT_LINE_ENDING_CR", "range": null, "title": "CR", "type": "bool"}], "depends_on": null, "help": "This option allows configuring the desired line endings sent to UART\nwhen a newline ('\\n', LF) appears on stdout.\nThree options are possible:\n\nCRLF: whenever LF is encountered, prepend it with CR\n\nLF: no modification is applied, stdout is sent as is\n\nCR: each occurence of LF is replaced with CR\n\nThis option doesn't affect behavior of the UART driver (drivers/uart.h).", "id": "component-config-newlib-line-ending-for-uart-output", "name": "NEWLIB_STDOUT_LINE_ENDING", "title": "Line ending for UART output", "type": "choice"}, {"children": [{"children": [], "depends_on": "<choice NEWLIB_STDIN_LINE_ENDING>", "help": null, "id": "NEWLIB_STDIN_LINE_ENDING_CRLF", "name": "NEWLIB_STDIN_LINE_ENDING_CRLF", "range": null, "title": "CRLF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDIN_LINE_ENDING>", "help": null, "id": "NEWLIB_STDIN_LINE_ENDING_LF", "name": "NEWLIB_STDIN_LINE_ENDING_LF", "range": null, "title": "LF", "type": "bool"}, {"children": [], "depends_on": "<choice NEWLIB_STDIN_LINE_ENDING>", "help": null, "id": "NEWLIB_STDIN_LINE_ENDING_CR", "name": "NEWLIB_STDIN_LINE_ENDING_CR", "range": null, "title": "CR", "type": "bool"}], "depends_on": null, "help": "This option allows configuring which input sequence on UART produces\na newline ('\\n', LF) on stdin.\nThree options are possible:\n\nCRLF: CRLF is converted to LF\n\nLF: no modification is applied, input is sent to stdin as is\n\nCR: each occurence of CR is replaced with LF\n\nThis option doesn't affect behavior of the UART driver (drivers/uart.h).", "id": "component-config-newlib-line-ending-for-uart-input", "name": "NEWLIB_STDIN_LINE_ENDING", "title": "Line ending for UART input", "type": "choice"}, {"children": [], "depends_on": null, "help": "ESP32 ROM contains parts of newlib C library, including printf/scanf family\nof functions. These functions have been compiled with so-called \"nano\"\nformatting option. This option doesn't support 64-bit integer formats and C99\nfeatures, such as positional arguments.\n\nFor more details about \"nano\" formatting option, please see newlib readme file,\nsearch for '--enable-newlib-nano-formatted-io':\nhttps://sourceware.org/newlib/README\n\nIf this option is enabled, build system will use functions available in\nROM, reducing the application binary size. Functions available in ROM run\nfaster than functions which run from flash. Functions available in ROM can\nalso run when flash instruction cache is disabled.\n\nIf you need 64-bit integer formatting support or C99 features, keep this\noption disabled.", "id": "NEWLIB_NANO_FORMAT", "name": "NEWLIB_NANO_FORMAT", "range": null, "title": "Enable 'nano' formatting options for printf/scanf family", "type": "bool"}], "depends_on": null, "id": "component-config-newlib", "title": "New<PERSON>b", "type": "menu"}, {"children": [{"children": [{"children": [], "depends_on": "SPI_FLASH_VERIFY_WRITE", "help": "If this option is enabled, if SPI flash write verification fails then a log error line\nwill be written with the address, expected & actual values. This can be useful when\ndebugging hardware SPI flash problems.", "id": "SPI_FLASH_LOG_FAILED_WRITE", "name": "SPI_FLASH_LOG_FAILED_WRITE", "range": null, "title": "Log errors if verification fails", "type": "bool"}, {"children": [], "depends_on": "SPI_FLASH_VERIFY_WRITE", "help": "If this option is enabled, any SPI flash write which tries to set zero bits in the flash to\nones will log a warning. Such writes will not result in the requested data appearing identically\nin flash once written, as SPI NOR flash can only set bits to one when an entire sector is erased.\nAfter erasing, individual bits can only be written from one to zero.\n\nNote that some software (such as SPIFFS) which is aware of SPI NOR flash may write one bits as an\noptimisation, relying on the data in flash becoming a bitwise AND of the new data and any existing data.\nSuch software will log spurious warnings if this option is enabled.", "id": "SPI_FLASH_WARN_SETTING_ZERO_TO_ONE", "name": "SPI_FLASH_WARN_SETTING_ZERO_TO_ONE", "range": null, "title": "Log warning if writing zero bits to ones", "type": "bool"}], "depends_on": null, "help": "If this option is enabled, any time SPI flash is written then the data will be read\nback and verified. This can catch hardware problems with SPI flash, or flash which\nwas not erased before verification.", "id": "SPI_FLASH_VERIFY_WRITE", "name": "SPI_FLASH_VERIFY_WRITE", "range": null, "title": "Verify SPI flash writes", "type": "bool"}, {"children": [], "depends_on": null, "help": "This option enables the following APIs:\n\n- spi_flash_reset_counters\n- spi_flash_dump_counters\n- spi_flash_get_counters\n\nThese APIs may be used to collect performance data for spi_flash APIs\nand to help understand behaviour of libraries which use SPI flash.", "id": "SPI_FLASH_ENABLE_COUNTERS", "name": "SPI_FLASH_ENABLE_COUNTERS", "range": null, "title": "Enable operation counters", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this flag to use patched versions of SPI flash ROM driver functions.\nThis option should be enabled, if any one of the following is true: (1) need to write\nto flash on ESP32-D2WD; (2) main SPI flash is connected to non-default pins; (3) main\nSPI flash chip is manufactured by ISSI.", "id": "SPI_FLASH_ROM_DRIVER_PATCH", "name": "SPI_FLASH_ROM_DRIVER_PATCH", "range": null, "title": "Enable SPI flash ROM driver patched functions", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32C3", "help": "Enable this flag to use new SPI flash driver functions from ROM instead of ESP-IDF.\n\nIf keeping this as \"n\" in your project, you will have less free IRAM.\nBut you can use all of our flash features.\n\nIf making this as \"y\" in your project, you will increase free IRAM.\nBut you may miss out on some flash features and support for new flash chips.\n\nCurrently the ROM cannot support the following features:\n\n- SPI_FLASH_AUTO_SUSPEND (C3, S3)", "id": "SPI_FLASH_ROM_IMPL", "name": "SPI_FLASH_ROM_IMPL", "range": null, "title": "Use esp_flash implementation in ROM", "type": "bool"}, {"children": [{"children": [], "depends_on": "<choice SPI_FLASH_DANGEROUS_WRITE>", "help": null, "id": "SPI_FLASH_DANGEROUS_WRITE_ABORTS", "name": "SPI_FLASH_DANGEROUS_WRITE_ABORTS", "range": null, "title": "Aborts", "type": "bool"}, {"children": [], "depends_on": "<choice SPI_FLASH_DANGEROUS_WRITE>", "help": null, "id": "SPI_FLASH_DANGEROUS_WRITE_FAILS", "name": "SPI_FLASH_DANGEROUS_WRITE_FAILS", "range": null, "title": "Fails", "type": "bool"}, {"children": [], "depends_on": "<choice SPI_FLASH_DANGEROUS_WRITE>", "help": null, "id": "SPI_FLASH_DANGEROUS_WRITE_ALLOWED", "name": "SPI_FLASH_DANGEROUS_WRITE_ALLOWED", "range": null, "title": "Allowed", "type": "bool"}], "depends_on": null, "help": "SPI flash APIs can optionally abort or return a failure code\nif erasing or writing addresses that fall at the beginning\nof flash (covering the bootloader and partition table) or that\noverlap the app partition that contains the running app.\n\nIt is not recommended to ever write to these regions from an IDF app,\nand this check prevents logic errors or corrupted firmware memory from\ndamaging these regions.\n\nNote that this feature *does not* check calls to the esp_rom_xxx SPI flash\nROM functions. These functions should not be called directly from IDF\napplications.", "id": "component-config-spi-flash-driver-writing-to-dangerous-flash-regions", "name": "SPI_FLASH_DANGEROUS_WRITE", "title": "Writing to dangerous flash regions", "type": "choice"}, {"children": [], "depends_on": null, "help": "The implementation of SPI flash has been greatly changed in IDF v4.0.\nEnable this option to use the legacy implementation.", "id": "SPI_FLASH_USE_LEGACY_IMPL", "name": "SPI_FLASH_USE_LEGACY_IMPL", "range": null, "title": "Use the legacy implementation before IDF v4.0", "type": "bool"}, {"children": [], "depends_on": "!SPI_FLASH_USE_LEGACY_IMPL && !IDF_TARGET_ESP32S2", "help": "Each SPI bus needs a lock for arbitration among devices. This allows multiple\ndevices on a same bus, but may reduce the speed of esp_flash driver access to the\nmain flash chip.\n\nIf you only need to use esp_flash driver to access the main flash chip, disable\nthis option, and the lock will be bypassed on SPI1 bus. Otherwise if extra devices\nare needed to attach to SPI1 bus, enable this option.", "id": "SPI_FLASH_SHARE_SPI1_BUS", "name": "SPI_FLASH_SHARE_SPI1_BUS", "range": null, "title": "Support other devices attached to SPI1 bus", "type": "bool"}, {"children": [], "depends_on": null, "help": "Some flash chips can have very high \"max\" erase times, especially for block erase (32KB or 64KB).\nThis option allows to bypass \"block erase\" and always do sector erase commands.\nThis will be much slower overall in most cases, but improves latency for other code to run.", "id": "SPI_FLASH_BYPASS_BLOCK_ERASE", "name": "SPI_FLASH_BYPASS_BLOCK_ERASE", "range": null, "title": "Bypass a block erase and always do sector erase", "type": "bool"}, {"children": [{"children": [], "depends_on": "SPI_FLASH_YIELD_DURING_ERASE", "help": "If a duration of one erase command is large\nthen it will yield CPUs after finishing a current command.", "id": "SPI_FLASH_ERASE_YIELD_DURATION_MS", "name": "SPI_FLASH_ERASE_YIELD_DURATION_MS", "range": null, "title": "Duration of erasing to yield CPUs (ms)", "type": "int"}, {"children": [], "depends_on": "SPI_FLASH_YIELD_DURING_ERASE", "help": "Defines how many ticks will be before returning to continue a erasing.", "id": "SPI_FLASH_ERASE_YIELD_TICKS", "name": "SPI_FLASH_ERASE_YIELD_TICKS", "range": null, "title": "CPU release time (tick) for an erase operation", "type": "int"}], "depends_on": null, "help": "This allows to yield the CPUs between erase commands.\nPrevents starvation of other tasks.", "id": "SPI_FLASH_YIELD_DURING_ERASE", "name": "SPI_FLASH_YIELD_DURING_ERASE", "range": null, "title": "Enables yield operation during flash erase", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32C3 && !SPI_FLASH_USE_LEGACY_IMPL && !SPI_FLASH_ROM_IMPL", "help": "This option is default n before ESP32-C3, because it needs bootloader support.\n\nCAUTION: If you want to OTA to an app with this feature turned on, please make\nsure the bootloader has the support for it. (later than IDF v4.3)\n\nAuto-suspend feature only supported by XMC chip.\nIf you are using an official module, please contact Espressif Business support.\nAlso reading auto suspend part in `SPI Flash API` document before you enable this function.", "id": "SPI_FLASH_AUTO_SUSPEND", "name": "SPI_FLASH_AUTO_SUSPEND", "range": null, "title": "Auto suspend long erase/write operations (READ DOCS FIRST)", "type": "bool"}, {"children": [], "depends_on": null, "help": "Flash write is broken down in terms of multiple (smaller) write operations.\nThis configuration options helps to set individual write chunk size, smaller\nvalue here ensures that cache (and non-IRAM resident interrupts) remains\ndisabled for shorter duration.", "id": "SPI_FLASH_WRITE_CHUNK_SIZE", "name": "SPI_FLASH_WRITE_CHUNK_SIZE", "range": [256, 8192], "title": "Flash write chunk size", "type": "int"}, {"children": [], "depends_on": null, "help": "SPI Flash driver uses the flash size configured in bootloader header by default.\nEnable this option to override flash size with latest ESPTOOLPY_FLASHSIZE value from\nthe app header if the size in the bootloader header is incorrect.", "id": "SPI_FLASH_SIZE_OVERRIDE", "name": "SPI_FLASH_SIZE_OVERRIDE", "range": null, "title": "Override flash size in bootloader header by ESPTOOLPY_FLASHSIZE", "type": "bool"}, {"children": [], "depends_on": "!SPI_FLASH_USE_LEGACY_IMPL", "help": "This option is helpful if you are using a flash chip whose timeout is quite large or unpredictable.", "id": "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED", "name": "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED", "range": null, "title": "Flash timeout checkout disabled", "type": "bool"}, {"children": [], "depends_on": "!SPI_FLASH_USE_LEGACY_IMPL", "help": "This option allows the chip driver list to be customized, instead of using the default list provided by\nESP-IDF.\n\nWhen this option is enabled, the default list is no longer compiled or linked. Instead, the\n`default_registered_chips` structure must be provided by the user.\n\nSee example: custom_chip_driver under examples/storage for more details.", "id": "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST", "name": "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST", "range": null, "title": "Override default chip driver list", "type": "bool"}, {"children": [{"children": [], "depends_on": null, "help": "Enable this to support auto detection of ISSI chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_ISSI_CHIP", "name": "SPI_FLASH_SUPPORT_ISSI_CHIP", "range": null, "title": "ISSI", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this to support auto detection of MXIC chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_MXIC_CHIP", "name": "SPI_FLASH_SUPPORT_MXIC_CHIP", "range": null, "title": "MXIC", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this to support auto detection of GD (GigaDevice) chips if chip vendor not\ndirectly given by ``chip_drv`` member of the chip struct. If you are using Wrover\nmodules, please don't disable this, otherwise your flash may not work in 4-bit\nmode.\n\nThis adds support for variant chips, however will extend detecting time and image\nsize. Note that the default chip driver supports the GD chips with product ID\n60H.", "id": "SPI_FLASH_SUPPORT_GD_CHIP", "name": "SPI_FLASH_SUPPORT_GD_CHIP", "range": null, "title": "GigaDevice", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this to support auto detection of Winbond chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_WINBOND_CHIP", "name": "SPI_FLASH_SUPPORT_WINBOND_CHIP", "range": null, "title": "Winbond", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this to support auto detection of BOYA chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_BOYA_CHIP", "name": "SPI_FLASH_SUPPORT_BOYA_CHIP", "range": null, "title": "BOYA", "type": "bool"}, {"children": [], "depends_on": null, "help": "Enable this to support auto detection of TH chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_TH_CHIP", "name": "SPI_FLASH_SUPPORT_TH_CHIP", "range": null, "title": "TH", "type": "bool"}, {"children": [], "depends_on": "IDF_TARGET_ESP32S3", "help": "Enable this to support auto detection of Octal MXIC chips if chip vendor not directly\ngiven by ``chip_drv`` member of the chip struct. This adds support for variant\nchips, however will extend detecting time.", "id": "SPI_FLASH_SUPPORT_MXIC_OPI_CHIP", "name": "SPI_FLASH_SUPPORT_MXIC_OPI_CHIP", "range": null, "title": "mxic (opi)", "type": "bool"}], "depends_on": null, "id": "component-config-spi-flash-driver-auto-detect-flash-chips", "title": "Auto-detect flash chips", "type": "menu"}, {"children": [], "depends_on": null, "help": "This option enables flash read/write operations to encrypted partition/s. This option\nis kept enabled irrespective of state of flash encryption feature. However, in case\napplication is not using flash encryption feature and is in need of some additional\nmemory from IRAM region (~1KB) then this config can be disabled.", "id": "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE", "name": "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE", "range": null, "title": "Enable encrypted partition read/write operations", "type": "bool"}], "depends_on": null, "id": "component-config-spi-flash-driver", "title": "SPI Flash driver", "type": "menu"}], "depends_on": null, "id": "component-config", "title": "Component config", "type": "menu"}, {"children": [{"children": [], "depends_on": null, "help": "Soc, esp32, and driver components, the most common\ncomponents. Some header of these components are included\nimplicitly by headers of other components before IDF v4.0.\nIt's not required for high-level components, but still\nincluded through long header chain everywhere.\n\nThis is harmful to the modularity. So it's changed in IDF\nv4.0.\n\nYou can still include these headers in a legacy way until it\nis totally deprecated by enable this option.", "id": "LEGACY_INCLUDE_COMMON_HEADERS", "name": "LEGACY_INCLUDE_COMMON_HEADERS", "range": null, "title": "Include headers across components as before IDF v4.0", "type": "bool"}], "depends_on": null, "id": "compatibility-options", "title": "Compatibility options", "type": "menu"}]