#include "driver/gpio.h"
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart.h"
#include "led.h"

// 仅声明全局变量，避免重复定义
extern unsigned char g_whiteled_state;
extern unsigned char g_whitle_count;

void _init_Led()
{

    gpio_pad_select_gpio(LED_WHITE_IO);
    gpio_pad_select_gpio(BEEP_IO);
    //gpio_pad_select_gpio(LED_YELLOW_IO);
    gpio_pad_select_gpio(GPIO1_IO);
    gpio_pad_select_gpio(GPIO2_IO);
    gpio_pad_select_gpio(GPIO3_IO);
    gpio_pad_select_gpio(GPIO4_IO);
    gpio_pad_select_gpio(RST_IO);
    gpio_pad_select_gpio(EN_IO);


    gpio_set_direction(LED_WHITE_IO, GPIO_MODE_OUTPUT);
    gpio_set_direction(BEEP_IO, GPIO_MODE_OUTPUT);
    //gpio_set_direction(LED_YELLOW_IO, GPIO_MODE_OUTPUT);    
    gpio_set_direction(GPIO1_IO, GPIO_MODE_OUTPUT); 
    gpio_set_direction(GPIO2_IO, GPIO_MODE_OUTPUT); 
    gpio_set_direction(GPIO3_IO, GPIO_MODE_OUTPUT); 
    gpio_set_direction(GPIO4_IO, GPIO_MODE_OUTPUT); 
    gpio_set_direction(RST_IO, GPIO_MODE_OUTPUT); 
    gpio_set_direction(EN_IO, GPIO_MODE_OUTPUT);
   
    _white_led_onoff(LED_ON);//led默认上电常亮一次
    g_whiteled_state = LED_ON; // 同步全局状态
    _beep_onoff(BEEP_OFF);//
    //_yellow_led__onoff(LED_ON);

    gpio_set_level(GPIO1_IO, LED_ON);
    gpio_set_level(GPIO2_IO, LED_ON);
    gpio_set_level(GPIO3_IO, LED_ON);
    gpio_set_level(GPIO4_IO, LED_ON);
    //
    //gpio_set_level(RST_IO, LED_ON);//????? ??1 off
   // vTaskDelay(10); //??1 ??10ms
    gpio_set_level(RST_IO, LED_OFF);//????? ??1 off
    gpio_set_level(EN_IO, LED_OFF);//???????? 1 off
    gpio_set_level(RST_IO, LED_ON);//????? ??1 off
    vTaskDelay(10); //??1 ??100ms
    gpio_set_level(RST_IO, LED_OFF);//????? ??1 off
}


void _yellow_led__onoff(int state)
{
    if(state==LED_ON)
    {
       gpio_set_level(LED_YELLOW_IO, LED_OFF);
    }
    else
    {
        gpio_set_level(LED_YELLOW_IO, LED_ON);
    }
}

//
void _white_led_onoff(int state)
{
    //gpio_set_level(LED_WHITE_IO, state);
    if(state==LED_ON)
    {
        gpio_set_level(LED_WHITE_IO, LED_ON);
    }
    else
    {
        gpio_set_level(LED_WHITE_IO, LED_OFF);
    }
}


// 切换白灯状态并同步全局变量。time 目前未使用，保留以兼容旧接口。
void _switch_white_led(unsigned char dummy, short time)
{
    (void)dummy;   // 兼容旧函数签名，实际未使用
    (void)time;

    if (g_whiteled_state == LED_ON)
   {
        _white_led_onoff(LED_OFF);
        g_whiteled_state = LED_OFF;
   }
   else
   {
        _white_led_onoff(LED_ON);
        g_whiteled_state = LED_ON;
   }
}

//
void _beep_onoff(int state)
{
    if(state==BEEP_ON)
    {
       gpio_set_level(BEEP_IO, BEEP_OFF);//
    }
    else
    {
        gpio_set_level(BEEP_IO, BEEP_ON);
    }
}

void _switch_beep(unsigned char state,short time)
 {
   // if( PART_STATE_ON == g_beep_state )
    {
        //if( g_beep_count >= 1 )
        {
            _beep_onoff(BEEP_ON);
        }
        //else if( g_beep_count >= 2 )
        {
            _beep_onoff(BEEP_OFF);    
            g_beep_count = 0;
        }
    }
    //else
    {
        //g_beep_count = 0;
    }
}

void testbeep(short time,char cishu)
{
    if(cishu)
    {
        for(short i=0;i<time;i++)
        {
            _beep_onoff(0x01); 
            vTaskDelay(30);
            _beep_onoff(0x00); 
         }
    }
    else
    {
        _beep_onoff(0x01);    
        vTaskDelay(time);
        _beep_onoff(0x00); 
    }
}
